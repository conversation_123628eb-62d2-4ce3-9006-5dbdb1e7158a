enum MediaType { video, audio, image, text }

enum FilterType {
  none,
  vintage,
  blackWhite,
  sepia,
  blur,
  sharpen,
  brightness,
  contrast,
  saturation,
  hue,
  vignette,
  warm,
  cool,
  dramatic
}

enum TransformType { none, scale, rotate, translate, flip, crop }

enum TransitionType {
  none,
  fade,
  slideLeft,
  slideRight,
  slideUp,
  slideDown,
  zoom,
  dissolve,
  wipe,
  circle,
}

class FilterSettings {
  final FilterType type;
  final double intensity;
  final Map<String, double> parameters;

  FilterSettings({
    required this.type,
    this.intensity = 1.0,
    this.parameters = const {},
  });

  FilterSettings copyWith({
    FilterType? type,
    double? intensity,
    Map<String, double>? parameters,
  }) {
    return FilterSettings(
      type: type ?? this.type,
      intensity: intensity ?? this.intensity,
      parameters: parameters ?? this.parameters,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'intensity': intensity,
      'parameters': parameters,
    };
  }

  factory FilterSettings.fromJson(Map<String, dynamic> json) {
    return FilterSettings(
      type: FilterType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => FilterType.none,
      ),
      intensity: json['intensity'] ?? 1.0,
      parameters: Map<String, double>.from(json['parameters'] ?? {}),
    );
  }
}

class TransformSettings {
  final TransformType type;
  final double scaleX;
  final double scaleY;
  final double rotation;
  final double translateX;
  final double translateY;
  final bool flipHorizontal;
  final bool flipVertical;
  final double cropLeft;
  final double cropTop;
  final double cropRight;
  final double cropBottom;

  TransformSettings({
    this.type = TransformType.none,
    this.scaleX = 1.0,
    this.scaleY = 1.0,
    this.rotation = 0.0,
    this.translateX = 0.0,
    this.translateY = 0.0,
    this.flipHorizontal = false,
    this.flipVertical = false,
    this.cropLeft = 0.0,
    this.cropTop = 0.0,
    this.cropRight = 0.0,
    this.cropBottom = 0.0,
  });

  TransformSettings copyWith({
    TransformType? type,
    double? scaleX,
    double? scaleY,
    double? rotation,
    double? translateX,
    double? translateY,
    bool? flipHorizontal,
    bool? flipVertical,
    double? cropLeft,
    double? cropTop,
    double? cropRight,
    double? cropBottom,
  }) {
    return TransformSettings(
      type: type ?? this.type,
      scaleX: scaleX ?? this.scaleX,
      scaleY: scaleY ?? this.scaleY,
      rotation: rotation ?? this.rotation,
      translateX: translateX ?? this.translateX,
      translateY: translateY ?? this.translateY,
      flipHorizontal: flipHorizontal ?? this.flipHorizontal,
      flipVertical: flipVertical ?? this.flipVertical,
      cropLeft: cropLeft ?? this.cropLeft,
      cropTop: cropTop ?? this.cropTop,
      cropRight: cropRight ?? this.cropRight,
      cropBottom: cropBottom ?? this.cropBottom,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'scaleX': scaleX,
      'scaleY': scaleY,
      'rotation': rotation,
      'translateX': translateX,
      'translateY': translateY,
      'flipHorizontal': flipHorizontal,
      'flipVertical': flipVertical,
      'cropLeft': cropLeft,
      'cropTop': cropTop,
      'cropRight': cropRight,
      'cropBottom': cropBottom,
    };
  }

  factory TransformSettings.fromJson(Map<String, dynamic> json) {
    return TransformSettings(
      type: TransformType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => TransformType.none,
      ),
      scaleX: json['scaleX'] ?? 1.0,
      scaleY: json['scaleY'] ?? 1.0,
      rotation: json['rotation'] ?? 0.0,
      translateX: json['translateX'] ?? 0.0,
      translateY: json['translateY'] ?? 0.0,
      flipHorizontal: json['flipHorizontal'] ?? false,
      flipVertical: json['flipVertical'] ?? false,
      cropLeft: json['cropLeft'] ?? 0.0,
      cropTop: json['cropTop'] ?? 0.0,
      cropRight: json['cropRight'] ?? 0.0,
      cropBottom: json['cropBottom'] ?? 0.0,
    );
  }
}

class TransitionSettings {
  final TransitionType type;
  final double duration; // Duration in seconds
  final double intensity;
  final Map<String, double> parameters;

  TransitionSettings({
    this.type = TransitionType.none,
    this.duration = 1.0,
    this.intensity = 1.0,
    this.parameters = const {},
  });

  TransitionSettings copyWith({
    TransitionType? type,
    double? duration,
    double? intensity,
    Map<String, double>? parameters,
  }) {
    return TransitionSettings(
      type: type ?? this.type,
      duration: duration ?? this.duration,
      intensity: intensity ?? this.intensity,
      parameters: parameters ?? this.parameters,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'duration': duration,
      'intensity': intensity,
      'parameters': parameters,
    };
  }

  factory TransitionSettings.fromJson(Map<String, dynamic> json) {
    return TransitionSettings(
      type: TransitionType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => TransitionType.none,
      ),
      duration: json['duration'] ?? 1.0,
      intensity: json['intensity'] ?? 1.0,
      parameters: Map<String, double>.from(json['parameters'] ?? {}),
    );
  }
}

class MediaItem {
  final String id;
  final MediaType type;
  final String? filePath;
  final String? text;
  final double startTime;
  final double endTime;
  final double? x;
  final double? y;
  final double? width;
  final double? height;
  final double? fontSize;
  final int? textColor;
  final double opacity;
  final double volume;
  final double
      videoVolume; // Volume for video items (separate from audio volume)
  final FilterSettings? filterSettings;
  final TransformSettings? transformSettings;
  final TransitionSettings? transitionSettings;
  final int layer; // For multi-layer support
  final double?
      originalStartOffset; // Offset from original video start (for trim)
  final double?
      originalEndOffset; // Offset from original video start (for trim)

  MediaItem({
    required this.id,
    required this.type,
    this.filePath,
    this.text,
    required this.startTime,
    required this.endTime,
    this.x,
    this.y,
    this.width,
    this.height,
    this.fontSize,
    this.textColor,
    this.opacity = 1.0,
    this.volume = 1.0,
    this.videoVolume = 1.0,
    this.filterSettings,
    this.transformSettings,
    this.transitionSettings,
    this.layer = 0,
    this.originalStartOffset,
    this.originalEndOffset,
  });

  MediaItem copyWith({
    String? id,
    MediaType? type,
    String? filePath,
    String? text,
    double? startTime,
    double? endTime,
    double? x,
    double? y,
    double? width,
    double? height,
    double? fontSize,
    int? textColor,
    double? opacity,
    double? volume,
    double? videoVolume,
    FilterSettings? filterSettings,
    TransformSettings? transformSettings,
    TransitionSettings? transitionSettings,
    int? layer,
    double? originalStartOffset,
    double? originalEndOffset,
  }) {
    return MediaItem(
      id: id ?? this.id,
      type: type ?? this.type,
      filePath: filePath ?? this.filePath,
      text: text ?? this.text,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      fontSize: fontSize ?? this.fontSize,
      textColor: textColor ?? this.textColor,
      opacity: opacity ?? this.opacity,
      volume: volume ?? this.volume,
      videoVolume: videoVolume ?? this.videoVolume,
      filterSettings: filterSettings ?? this.filterSettings,
      transformSettings: transformSettings ?? this.transformSettings,
      transitionSettings: transitionSettings ?? this.transitionSettings,
      layer: layer ?? this.layer,
      originalStartOffset: originalStartOffset ?? this.originalStartOffset,
      originalEndOffset: originalEndOffset ?? this.originalEndOffset,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'filePath': filePath,
      'text': text,
      'startTime': startTime,
      'endTime': endTime,
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'fontSize': fontSize,
      'textColor': textColor,
      'opacity': opacity,
      'volume': volume,
      'videoVolume': videoVolume,
      'filterSettings': filterSettings?.toJson(),
      'transformSettings': transformSettings?.toJson(),
      'transitionSettings': transitionSettings?.toJson(),
      'layer': layer,
      'originalStartOffset': originalStartOffset,
      'originalEndOffset': originalEndOffset,
    };
  }

  factory MediaItem.fromJson(Map<String, dynamic> json) {
    return MediaItem(
      id: json['id'],
      type: MediaType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      filePath: json['filePath'],
      text: json['text'],
      startTime: json['startTime'],
      endTime: json['endTime'],
      x: json['x'],
      y: json['y'],
      width: json['width'],
      height: json['height'],
      fontSize: json['fontSize'],
      textColor: json['textColor'],
      opacity: json['opacity'] ?? 1.0,
      volume: json['volume'] ?? 1.0,
      videoVolume: json['videoVolume'] ?? 1.0,
      filterSettings: json['filterSettings'] != null
          ? FilterSettings.fromJson(json['filterSettings'])
          : null,
      transformSettings: json['transformSettings'] != null
          ? TransformSettings.fromJson(json['transformSettings'])
          : null,
      transitionSettings: json['transitionSettings'] != null
          ? TransitionSettings.fromJson(json['transitionSettings'])
          : null,
      layer: json['layer'] ?? 0,
      originalStartOffset: json['originalStartOffset'],
      originalEndOffset: json['originalEndOffset'],
    );
  }
}

class VideoProject {
  final String id;
  final String name;
  final List<MediaItem> mediaItems;
  final double duration;
  final DateTime createdAt;
  final DateTime updatedAt;

  VideoProject({
    required this.id,
    required this.name,
    required this.mediaItems,
    required this.duration,
    required this.createdAt,
    required this.updatedAt,
  });

  // Get the main video (first video in layer 0)
  String? get mainVideoPath {
    final videoItems = mediaItems
        .where((item) => item.type == MediaType.video)
        .toList()
      ..sort((a, b) => a.layer.compareTo(b.layer));

    return videoItems.isNotEmpty ? videoItems.first.filePath : null;
  }

  // Get all video items sorted by layer
  List<MediaItem> get videoItems {
    return mediaItems.where((item) => item.type == MediaType.video).toList()
      ..sort((a, b) => a.layer.compareTo(b.layer));
  }

  VideoProject copyWith({
    String? id,
    String? name,
    List<MediaItem>? mediaItems,
    double? duration,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VideoProject(
      id: id ?? this.id,
      name: name ?? this.name,
      mediaItems: mediaItems ?? this.mediaItems,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mediaItems': mediaItems.map((item) => item.toJson()).toList(),
      'duration': duration,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory VideoProject.fromJson(Map<String, dynamic> json) {
    return VideoProject(
      id: json['id'],
      name: json['name'],
      mediaItems: (json['mediaItems'] as List)
          .map((item) => MediaItem.fromJson(item))
          .toList(),
      duration: json['duration'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

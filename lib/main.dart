import 'package:flutter/material.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() {
  runApp(const VideoEditorApp());
}

class VideoEditorApp extends StatelessWidget {
  const VideoEditorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Video Editor',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
        colorScheme: ColorScheme.dark(
          primary: Color(AppConstants.primaryColor),
          secondary: Color(AppConstants.accentColor),
          surface: Color(AppConstants.surfaceColor),
        ),
        scaffoldBackgroundColor: Color(AppConstants.backgroundColor),
        appBarTheme: AppBarTheme(
          backgroundColor: Color(AppConstants.surfaceColor),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
      ),
      home: const HomeScreen(),
    );
  }
}

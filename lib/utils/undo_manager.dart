import '../models/video_project.dart';

/// Represents an action that can be undone/redone
abstract class UndoableAction {
  void execute();
  void undo();
  String get description;
}

/// Action for deleting a media item
class DeleteMediaItemAction extends UndoableAction {
  final MediaItem item;
  final List<MediaItem> mediaItems;
  final Function(MediaItem) onRestore;

  DeleteMediaItemAction({
    required this.item,
    required this.mediaItems,
    required this.onRestore,
  });

  @override
  void execute() {
    mediaItems.removeWhere((element) => element.id == item.id);
  }

  @override
  void undo() {
    onRestore(item);
  }

  @override
  String get description => 'Delete ${item.type.name}';
}

/// Action for deleting multiple media items
class DeleteMultipleItemsAction extends UndoableAction {
  final List<MediaItem> items;
  final List<MediaItem> mediaItems;
  final Function(List<MediaItem>) onRestore;

  DeleteMultipleItemsAction({
    required this.items,
    required this.mediaItems,
    required this.onRestore,
  });

  @override
  void execute() {
    for (final item in items) {
      mediaItems.removeWhere((element) => element.id == item.id);
    }
  }

  @override
  void undo() {
    onRestore(items);
  }

  @override
  String get description => 'Delete ${items.length} items';
}

/// Manages undo/redo operations
class UndoManager {
  final List<UndoableAction> _undoStack = [];
  final List<UndoableAction> _redoStack = [];
  static const int maxStackSize = 50;

  /// Execute an action and add it to the undo stack
  void executeAction(UndoableAction action) {
    action.execute();
    _undoStack.add(action);
    _redoStack.clear(); // Clear redo stack when new action is performed

    // Limit stack size
    if (_undoStack.length > maxStackSize) {
      _undoStack.removeAt(0);
    }
  }

  /// Undo the last action
  bool undo() {
    if (_undoStack.isEmpty) return false;

    final action = _undoStack.removeLast();
    action.undo();
    _redoStack.add(action);

    return true;
  }

  /// Redo the last undone action
  bool redo() {
    if (_redoStack.isEmpty) return false;

    final action = _redoStack.removeLast();
    action.execute();
    _undoStack.add(action);

    return true;
  }

  /// Check if undo is available
  bool get canUndo => _undoStack.isNotEmpty;

  /// Check if redo is available
  bool get canRedo => _redoStack.isNotEmpty;

  /// Get description of the last action that can be undone
  String? get undoDescription =>
      _undoStack.isNotEmpty ? _undoStack.last.description : null;

  /// Get description of the last action that can be redone
  String? get redoDescription =>
      _redoStack.isNotEmpty ? _redoStack.last.description : null;

  /// Clear all undo/redo history
  void clear() {
    _undoStack.clear();
    _redoStack.clear();
  }
}

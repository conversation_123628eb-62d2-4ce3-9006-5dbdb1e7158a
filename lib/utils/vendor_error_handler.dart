import 'package:flutter/foundation.dart';

/// Utility class to handle vendor-specific errors (OnePlus/OPPO/etc.)
class VendorErrorHandler {
  /// Check if an error is a vendor-specific media property error
  static bool isVendorMediaPropertyError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    return errorString.contains('vendor.oplus.media') ||
           errorString.contains('vendor.oppo.media') ||
           errorString.contains('vendor.oplus') ||
           errorString.contains('vendor.oppo') ||
           errorString.contains('access denied finding property') ||
           errorString.contains('vpp.stutter') ||
           errorString.contains('media.vpp') ||
           errorString.contains('vendor.media');
  }
  
  /// Handle vendor-specific errors safely
  static T handleVendorError<T>(
    dynamic error, {
    required T fallbackValue,
    String? context,
    VoidCallback? onVendorError,
  }) {
    if (isVendorMediaPropertyError(error)) {
      // Log the vendor error for debugging but don't crash
      debugPrint('Vendor media property error${context != null ? ' in $context' : ''}: $error');
      debugPrint('This is a known issue on OnePlus/OPPO devices and can be safely ignored.');
      
      // Execute callback if provided
      onVendorError?.call();
      
      return fallbackValue;
    } else {
      // Re-throw non-vendor errors
      throw error;
    }
  }
  
  /// Execute a function with vendor error handling
  static Future<T> executeWithVendorErrorHandling<T>(
    Future<T> Function() function, {
    required T fallbackValue,
    String? context,
    VoidCallback? onVendorError,
  }) async {
    try {
      return await function();
    } catch (error) {
      return handleVendorError(
        error,
        fallbackValue: fallbackValue,
        context: context,
        onVendorError: onVendorError,
      );
    }
  }
  
  /// Execute a synchronous function with vendor error handling
  static T executeSyncWithVendorErrorHandling<T>(
    T Function() function, {
    required T fallbackValue,
    String? context,
    VoidCallback? onVendorError,
  }) {
    try {
      return function();
    } catch (error) {
      return handleVendorError(
        error,
        fallbackValue: fallbackValue,
        context: context,
        onVendorError: onVendorError,
      );
    }
  }
  
  /// Get device vendor information
  static String getDeviceVendor() {
    // This would typically use device_info_plus package
    // For now, return unknown
    return 'unknown';
  }
  
  /// Check if current device is known to have vendor property issues
  static bool isKnownProblematicDevice() {
    final vendor = getDeviceVendor().toLowerCase();
    return vendor.contains('oneplus') || 
           vendor.contains('oppo') || 
           vendor.contains('realme') ||
           vendor.contains('vivo');
  }
  
  /// Log vendor error information for debugging
  static void logVendorErrorInfo(dynamic error, String context) {
    if (kDebugMode && isVendorMediaPropertyError(error)) {
      print('=== VENDOR ERROR INFO ===');
      print('Context: $context');
      print('Error: $error');
      print('Device Vendor: ${getDeviceVendor()}');
      print('Is Problematic Device: ${isKnownProblematicDevice()}');
      print('Recommendation: This error can be safely ignored on OnePlus/OPPO devices');
      print('========================');
    }
  }
}

/// Extension to add vendor error handling to Future
extension VendorErrorHandlingFuture<T> on Future<T> {
  Future<T> handleVendorErrors({
    required T fallbackValue,
    String? context,
    VoidCallback? onVendorError,
  }) {
    return VendorErrorHandler.executeWithVendorErrorHandling(
      () => this,
      fallbackValue: fallbackValue,
      context: context,
      onVendorError: onVendorError,
    );
  }
}

import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/video_project.dart';

class VideoExportService {
  static const String _exportFolderName = 'VideoEditor';

  /// Export video project to Downloads folder with progress callback
  static Future<String?> exportVideo({
    required VideoProject project,
    required Function(double progress) onProgress,
    required Function(String message) onStatusUpdate,
  }) async {
    try {
      onStatusUpdate('Preparing export...');
      onProgress(0.0);

      // Request storage permission (only for Android)
      if (Platform.isAndroid) {
        if (!await _requestStoragePermission()) {
          throw Exception(
              'Storage permission denied. Please grant storage access in app settings.');
        }
      }

      onStatusUpdate('Getting Downloads folder...');
      onProgress(0.1);

      // Get Downloads directory
      final downloadsDir = await getDownloadsDirectory();
      if (downloadsDir == null) {
        throw Exception('Could not access Downloads folder');
      }

      // Create VideoEditor subfolder
      final exportDir = Directory('${downloadsDir.path}/$_exportFolderName');
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      onStatusUpdate('Generating video filename...');
      onProgress(0.2);

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filename =
          'video_${project.name.replaceAll(' ', '_')}_$timestamp.mp4';
      final outputPath = '${exportDir.path}/$filename';

      onStatusUpdate('Processing video items...');
      onProgress(0.3);

      // Get all video items sorted by start time
      final videoItems = project.mediaItems
          .where(
              (item) => item.type == MediaType.video && item.filePath != null)
          .toList()
        ..sort((a, b) => a.startTime.compareTo(b.startTime));

      if (videoItems.isEmpty) {
        throw Exception('No video items found in project');
      }

      onStatusUpdate('Combining videos...');
      onProgress(0.4);

      // For now, we'll copy the first video as a simple implementation
      // In a real app, you'd use FFmpeg or similar to combine videos with transitions
      await _combineVideos(videoItems, outputPath, onProgress, onStatusUpdate);

      onStatusUpdate('Export completed!');
      onProgress(1.0);

      return outputPath;
    } catch (e) {
      onStatusUpdate('Export failed: ${e.toString()}');
      throw e;
    }
  }

  /// Request storage permission
  static Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      // For Android 11+ (API 30+), we need different permissions
      try {
        // First try to get storage permission
        var status = await Permission.storage.status;

        if (status.isDenied) {
          status = await Permission.storage.request();
        }

        // If still denied, try manage external storage for Android 11+
        if (status.isDenied || status.isPermanentlyDenied) {
          final manageStatus = await Permission.manageExternalStorage.status;
          if (manageStatus.isDenied) {
            final newStatus = await Permission.manageExternalStorage.request();
            return newStatus.isGranted;
          }
          return manageStatus.isGranted;
        }

        return status.isGranted;
      } catch (e) {
        print('Permission error: $e');
        return false;
      }
    }
    return true; // iOS, Windows, and other platforms don't need explicit permission
  }

  /// Get Downloads directory
  static Future<Directory?> getDownloadsDirectory() async {
    try {
      if (Platform.isAndroid) {
        // Try to get Downloads directory
        final directory = Directory('/storage/emulated/0/Download');
        if (await directory.exists()) {
          return directory;
        }

        // Fallback to external storage
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          final downloadsDir = Directory('${externalDir.path}/Download');
          if (!await downloadsDir.exists()) {
            await downloadsDir.create(recursive: true);
          }
          return downloadsDir;
        }
      } else if (Platform.isWindows) {
        // Windows Downloads folder - try multiple approaches
        final userProfile = Platform.environment['USERPROFILE'];
        if (userProfile != null) {
          final downloadsDir = Directory('$userProfile\\Downloads');
          if (await downloadsDir.exists()) {
            return downloadsDir;
          }
        }

        // Try alternative Windows paths
        final username = Platform.environment['USERNAME'];
        if (username != null) {
          final altDownloadsDir = Directory('C:\\Users\\<USER>\\Downloads');
          if (await altDownloadsDir.exists()) {
            return altDownloadsDir;
          }
        }

        // Fallback to Documents folder
        try {
          final documentsDir = await getApplicationDocumentsDirectory();
          final downloadsDir = Directory('${documentsDir.path}\\Downloads');
          if (!await downloadsDir.exists()) {
            await downloadsDir.create(recursive: true);
          }
          return downloadsDir;
        } catch (e) {
          // If Documents fails, use app directory
          final appDir = await getApplicationSupportDirectory();
          final downloadsDir = Directory('${appDir.path}\\Downloads');
          if (!await downloadsDir.exists()) {
            await downloadsDir.create(recursive: true);
          }
          return downloadsDir;
        }
      } else if (Platform.isMacOS) {
        // macOS Downloads folder
        final homeDir = Platform.environment['HOME'];
        if (homeDir != null) {
          final downloadsDir = Directory('$homeDir/Downloads');
          if (await downloadsDir.exists()) {
            return downloadsDir;
          }
        }

        // Fallback to Documents
        final documentsDir = await getApplicationDocumentsDirectory();
        final downloadsDir = Directory('${documentsDir.path}/Downloads');
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }
        return downloadsDir;
      }

      // Fallback to app documents directory for other platforms
      final appDir = await getApplicationDocumentsDirectory();
      final downloadsDir = Directory('${appDir.path}/Downloads');
      if (!await downloadsDir.exists()) {
        await downloadsDir.create(recursive: true);
      }
      return downloadsDir;
    } catch (e) {
      print('Error getting downloads directory: $e');
      // Last resort - use temporary directory
      final tempDir = await getTemporaryDirectory();
      final downloadsDir = Directory('${tempDir.path}/VideoExports');
      if (!await downloadsDir.exists()) {
        await downloadsDir.create(recursive: true);
      }
      return downloadsDir;
    }
  }

  /// Combine videos (simplified implementation)
  static Future<void> _combineVideos(
    List<MediaItem> videoItems,
    String outputPath,
    Function(double progress) onProgress,
    Function(String message) onStatusUpdate,
  ) async {
    if (videoItems.length == 1) {
      // Single video - just copy it
      onStatusUpdate('Copying video file...');
      final sourceFile = File(videoItems.first.filePath!);
      await sourceFile.copy(outputPath);
      onProgress(1.0);
    } else {
      // Multiple videos - for now, copy the first one
      // In a real implementation, you'd use FFmpeg to concatenate videos
      onStatusUpdate('Combining multiple videos...');

      for (int i = 0; i < videoItems.length; i++) {
        final progress = 0.4 + (0.6 * (i + 1) / videoItems.length);
        onProgress(progress);
        onStatusUpdate('Processing video ${i + 1} of ${videoItems.length}...');

        if (i == 0) {
          // Copy first video
          final sourceFile = File(videoItems[i].filePath!);
          await sourceFile.copy(outputPath);
        } else {
          // For subsequent videos, we'd append them using FFmpeg
          // For now, we'll just use the first video
          break;
        }

        // Simulate processing time
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }
  }

  /// Get file size in MB
  static Future<double> getFileSizeMB(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      final bytes = await file.length();
      return bytes / (1024 * 1024);
    }
    return 0.0;
  }

  /// Open Downloads folder
  static Future<void> openDownloadsFolder() async {
    final downloadsDir = await getDownloadsDirectory();
    if (downloadsDir != null && Platform.isWindows) {
      // Open folder in Windows Explorer
      await Process.run('explorer', [downloadsDir.path]);
    }
  }
}

import 'dart:io';
import 'dart:async';
import '../models/video_project.dart';

/// Service for handling temporary video editing operations
/// This service provides non-destructive editing capabilities
/// where changes are applied virtually without modifying the original file
class TemporaryVideoService {
  static final TemporaryVideoService _instance = TemporaryVideoService._internal();
  factory TemporaryVideoService() => _instance;
  TemporaryVideoService._internal();

  static TemporaryVideoService get instance => _instance;

  // Cache for temporary edit states
  final Map<String, TemporaryEditState> _temporaryStates = {};

  /// Create a temporary edit state for a media item
  TemporaryEditState createTemporaryEditState({
    required String mediaItemId,
    required String originalFilePath,
    required double originalDuration,
  }) {
    final state = TemporaryEditState(
      originalFilePath: originalFilePath,
      originalDuration: originalDuration,
      isActive: false,
    );
    
    _temporaryStates[mediaItemId] = state;
    return state;
  }

  /// Apply trim to temporary state
  TemporaryEditState applyTemporaryTrim({
    required String mediaItemId,
    required double trimStart,
    required double trimEnd,
  }) {
    final currentState = _temporaryStates[mediaItemId];
    if (currentState == null) {
      throw Exception('No temporary state found for media item: $mediaItemId');
    }

    final updatedState = currentState.copyWith(
      trimStart: trimStart,
      trimEnd: trimEnd,
      isActive: true,
    );

    _temporaryStates[mediaItemId] = updatedState;
    return updatedState;
  }

  /// Apply cut to temporary state
  TemporaryEditState applyTemporaryCut({
    required String mediaItemId,
    required double cutStart,
    required double cutEnd,
  }) {
    final currentState = _temporaryStates[mediaItemId];
    if (currentState == null) {
      throw Exception('No temporary state found for media item: $mediaItemId');
    }

    final cutId = 'cut_${DateTime.now().millisecondsSinceEpoch}';
    final newCutPoint = CutPoint(
      id: cutId,
      startTime: cutStart,
      endTime: cutEnd,
    );

    final updatedCuts = List<CutPoint>.from(currentState.cutPoints)..add(newCutPoint);
    
    final updatedState = currentState.copyWith(
      cutPoints: updatedCuts,
      isActive: true,
    );

    _temporaryStates[mediaItemId] = updatedState;
    return updatedState;
  }

  /// Remove a specific cut point
  TemporaryEditState removeCutPoint({
    required String mediaItemId,
    required String cutId,
  }) {
    final currentState = _temporaryStates[mediaItemId];
    if (currentState == null) {
      throw Exception('No temporary state found for media item: $mediaItemId');
    }

    final updatedCuts = currentState.cutPoints
        .where((cut) => cut.id != cutId)
        .toList();
    
    final updatedState = currentState.copyWith(
      cutPoints: updatedCuts,
      isActive: updatedCuts.isNotEmpty || 
                currentState.trimStart != null || 
                currentState.trimEnd != null,
    );

    _temporaryStates[mediaItemId] = updatedState;
    return updatedState;
  }

  /// Reset temporary edits for a media item
  void resetTemporaryEdits(String mediaItemId) {
    final currentState = _temporaryStates[mediaItemId];
    if (currentState != null) {
      final resetState = currentState.copyWith(
        trimStart: null,
        trimEnd: null,
        cutPoints: [],
        isActive: false,
      );
      _temporaryStates[mediaItemId] = resetState;
    }
  }

  /// Get temporary edit state for a media item
  TemporaryEditState? getTemporaryEditState(String mediaItemId) {
    return _temporaryStates[mediaItemId];
  }

  /// Check if media item has active temporary edits
  bool hasActiveTemporaryEdits(String mediaItemId) {
    final state = _temporaryStates[mediaItemId];
    return state?.isActive == true;
  }

  /// Convert virtual timeline position to original file position
  double virtualToOriginalPosition({
    required String mediaItemId,
    required double virtualPosition,
  }) {
    final state = _temporaryStates[mediaItemId];
    if (state?.isActive != true) {
      return virtualPosition;
    }

    final segments = state!.getTimelineSegments();
    for (final segment in segments) {
      if (virtualPosition >= segment.virtualStart && 
          virtualPosition <= segment.virtualEnd) {
        final relativePosition = virtualPosition - segment.virtualStart;
        return segment.originalStart + relativePosition;
      }
    }
    
    return virtualPosition;
  }

  /// Convert original file position to virtual timeline position
  double originalToVirtualPosition({
    required String mediaItemId,
    required double originalPosition,
  }) {
    final state = _temporaryStates[mediaItemId];
    if (state?.isActive != true) {
      return originalPosition;
    }

    final segments = state!.getTimelineSegments();
    for (final segment in segments) {
      if (originalPosition >= segment.originalStart && 
          originalPosition <= segment.originalEnd) {
        final relativePosition = originalPosition - segment.originalStart;
        return segment.virtualStart + relativePosition;
      }
    }
    
    // Position is in a cut section, return nearest virtual position
    for (int i = 0; i < segments.length; i++) {
      if (originalPosition < segments[i].originalStart) {
        return i > 0 ? segments[i - 1].virtualEnd : 0.0;
      }
    }
    
    return segments.isNotEmpty ? segments.last.virtualEnd : 0.0;
  }

  /// Get effective duration after temporary edits
  double getEffectiveDuration(String mediaItemId) {
    final state = _temporaryStates[mediaItemId];
    if (state?.isActive == true) {
      return state!.getEffectiveDuration();
    }
    return state?.originalDuration ?? 0.0;
  }

  /// Get timeline segments for visualization
  List<TimelineSegment> getTimelineSegments(String mediaItemId) {
    final state = _temporaryStates[mediaItemId];
    if (state?.isActive == true) {
      return state!.getTimelineSegments();
    }
    
    // Return single segment for unedited video
    if (state != null) {
      return [TimelineSegment(
        originalStart: 0.0,
        originalEnd: state.originalDuration,
        virtualStart: 0.0,
        virtualEnd: state.originalDuration,
      )];
    }
    
    return [];
  }

  /// Check if a virtual position is visible (not in a cut section)
  bool isPositionVisible({
    required String mediaItemId,
    required double virtualPosition,
  }) {
    final state = _temporaryStates[mediaItemId];
    if (state?.isActive != true) {
      return true;
    }

    final segments = state!.getTimelineSegments();
    for (final segment in segments) {
      if (virtualPosition >= segment.virtualStart && 
          virtualPosition <= segment.virtualEnd) {
        return true;
      }
    }
    return false;
  }

  /// Clear all temporary states (useful for cleanup)
  void clearAllTemporaryStates() {
    _temporaryStates.clear();
  }

  /// Get all media items with active temporary edits
  List<String> getMediaItemsWithActiveEdits() {
    return _temporaryStates.entries
        .where((entry) => entry.value.isActive)
        .map((entry) => entry.key)
        .toList();
  }

  /// Preview the result of temporary edits without applying them
  /// This method simulates what the final video would look like
  Future<Map<String, dynamic>> previewTemporaryEdits(String mediaItemId) async {
    final state = _temporaryStates[mediaItemId];
    if (state?.isActive != true) {
      return {
        'hasEdits': false,
        'originalDuration': state?.originalDuration ?? 0.0,
        'effectiveDuration': state?.originalDuration ?? 0.0,
        'segments': <TimelineSegment>[],
      };
    }

    final segments = state!.getTimelineSegments();
    final effectiveDuration = state.getEffectiveDuration();

    return {
      'hasEdits': true,
      'originalDuration': state.originalDuration,
      'effectiveDuration': effectiveDuration,
      'segments': segments,
      'trimStart': state.trimStart,
      'trimEnd': state.trimEnd,
      'cutPoints': state.cutPoints,
    };
  }
}

import 'package:flutter/material.dart';
import '../models/video_project.dart';

class ResizableWidget extends StatefulWidget {
  final Widget child;
  final MediaItem mediaItem;
  final Function(MediaItem) onChanged;
  final bool isSelected;
  final VoidCallback? onTap;

  const ResizableWidget({
    super.key,
    required this.child,
    required this.mediaItem,
    required this.onChanged,
    this.isSelected = false,
    this.onTap,
  });

  @override
  State<ResizableWidget> createState() => _ResizableWidgetState();
}

class _ResizableWidgetState extends State<ResizableWidget> {
  late double _width;
  late double _height;
  late double _x;
  late double _y;

  bool _isDragging = false;
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  @override
  void didUpdateWidget(ResizableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.mediaItem != widget.mediaItem) {
      _initializeValues();
    }
  }

  void _initializeValues() {
    _width = widget.mediaItem.width ?? 200;
    _height = widget.mediaItem.height ?? 150;
    _x = widget.mediaItem.x ?? 50;
    _y = widget.mediaItem.y ?? 50;
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _x,
      top: _y,
      child: GestureDetector(
        onTap: widget.onTap,
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: Container(
          width: _width,
          height: _height,
          decoration: widget.isSelected
              ? BoxDecoration(
                  border: Border.all(
                    color: Colors.blue,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                )
              : BoxDecoration(
                  border: Border.all(
                    color: Colors.transparent,
                    width: 2,
                  ),
                ),
          child: Stack(
            children: [
              // Main content
              Positioned.fill(
                child: widget.child,
              ),

              // Resize handles (only show when selected)
              if (widget.isSelected) ..._buildResizeHandles(),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildResizeHandles() {
    const handleSize = 12.0;
    const handleColor = Colors.blue;

    return [
      // Top-left handle
      Positioned(
        left: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.topLeft),
        ),
      ),

      // Top-right handle
      Positioned(
        right: -handleSize / 2,
        top: -handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.topRight),
        ),
      ),

      // Bottom-left handle
      Positioned(
        left: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.bottomLeft),
        ),
      ),

      // Bottom-right handle
      Positioned(
        right: -handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.bottomRight),
        ),
      ),

      // Top center handle
      Positioned(
        left: _width / 2 - handleSize / 2,
        top: -handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.top),
        ),
      ),

      // Bottom center handle
      Positioned(
        left: _width / 2 - handleSize / 2,
        bottom: -handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.bottom),
        ),
      ),

      // Left center handle
      Positioned(
        left: -handleSize / 2,
        top: _height / 2 - handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.left),
        ),
      ),

      // Right center handle
      Positioned(
        right: -handleSize / 2,
        top: _height / 2 - handleSize / 2,
        child: _buildHandle(
          handleColor,
          handleSize,
          (details) => _onResizeHandle(details, ResizeDirection.right),
        ),
      ),
    ];
  }

  Widget _buildHandle(Color color, double size, Function(DragUpdateDetails) onPanUpdate) {
    return GestureDetector(
      onPanStart: (details) {
        setState(() {
          _isResizing = true;
        });
      },
      onPanUpdate: onPanUpdate,
      onPanEnd: (details) {
        setState(() {
          _isResizing = false;
        });
        _updateMediaItem();
      },
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 1),
        ),
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    if (!_isResizing) {
      setState(() {
        _isDragging = true;
      });
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_isDragging && !_isResizing) {
      setState(() {
        _x += details.delta.dx;
        _y += details.delta.dy;

        // Constrain to screen bounds
        _x = _x.clamp(0.0, MediaQuery.of(context).size.width - _width);
        _y = _y.clamp(0.0, MediaQuery.of(context).size.height - _height);
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_isDragging) {
      setState(() {
        _isDragging = false;
      });
      _updateMediaItem();
    }
  }

  void _onResizeHandle(DragUpdateDetails details, ResizeDirection direction) {
    setState(() {
      final delta = details.delta;

      switch (direction) {
        case ResizeDirection.topLeft:
          _width -= delta.dx;
          _height -= delta.dy;
          _x += delta.dx;
          _y += delta.dy;
          break;
        case ResizeDirection.topRight:
          _width += delta.dx;
          _height -= delta.dy;
          _y += delta.dy;
          break;
        case ResizeDirection.bottomLeft:
          _width -= delta.dx;
          _height += delta.dy;
          _x += delta.dx;
          break;
        case ResizeDirection.bottomRight:
          _width += delta.dx;
          _height += delta.dy;
          break;
        case ResizeDirection.top:
          _height -= delta.dy;
          _y += delta.dy;
          break;
        case ResizeDirection.bottom:
          _height += delta.dy;
          break;
        case ResizeDirection.left:
          _width -= delta.dx;
          _x += delta.dx;
          break;
        case ResizeDirection.right:
          _width += delta.dx;
          break;
      }

      // Constrain minimum size
      _width = _width.clamp(50.0, double.infinity);
      _height = _height.clamp(50.0, double.infinity);

      // Constrain position to screen bounds
      _x = _x.clamp(0.0, MediaQuery.of(context).size.width - _width);
      _y = _y.clamp(0.0, MediaQuery.of(context).size.height - _height);
    });
  }

  void _updateMediaItem() {
    final updatedItem = widget.mediaItem.copyWith(
      x: _x,
      y: _y,
      width: _width,
      height: _height,
    );
    widget.onChanged(updatedItem);
  }
}

enum ResizeDirection {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  top,
  bottom,
  left,
  right,
}

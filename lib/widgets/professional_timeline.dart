import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/video_project.dart';
import '../utils/constants.dart';

class ProfessionalTimeline extends StatefulWidget {
  final double duration;
  final double currentPosition;
  final List<MediaItem> mediaItems;
  final Function(double) onPositionChanged;
  final Function(MediaItem) onMediaItemTap;
  final Function(MediaItem) onMediaItemDelete;
  final Function(MediaItem, double, double) onMediaItemResize;
  final Function(MediaItem, double) onMediaItemMove;
  final Function(double)? onVideoTransition; // Callback for video transitions

  // Trim/Cut functionality
  final bool isTrimMode;
  final bool isCutMode;
  final double? trimStart;
  final double? trimEnd;
  final Function(double)? onTrimStartChanged;
  final Function(double)? onTrimEndChanged;

  const ProfessionalTimeline({
    super.key,
    required this.duration,
    required this.currentPosition,
    required this.mediaItems,
    required this.onPositionChanged,
    required this.onMediaItemTap,
    required this.onMediaItemDelete,
    required this.onMediaItemResize,
    required this.onMediaItemMove,
    this.onVideoTransition,
    this.isTrimMode = false,
    this.isCutMode = false,
    this.trimStart,
    this.trimEnd,
    this.onTrimStartChanged,
    this.onTrimEndChanged,
  });

  @override
  State<ProfessionalTimeline> createState() => _ProfessionalTimelineState();
}

class _ProfessionalTimelineState extends State<ProfessionalTimeline>
    with TickerProviderStateMixin {
  double _zoomLevel = 1.0;
  bool _isDragging = false;
  double _dragPosition = 0;

  // Media item dragging state
  MediaItem? _draggingItem;
  bool _isDraggingItem = false;
  double _dragStartX = 0;
  double _originalStartTime = 0;
  double _originalEndTime = 0;

  // Resize state
  MediaItem? _resizingItem;
  bool _isResizingStart = false;
  bool _isResizingEnd = false;

  // Animation for cut mode
  late AnimationController _cutAnimationController;
  late Animation<double> _cutAnimation;

  // Video transition tracking
  double _lastPosition = 0.0;
  String? _currentActiveVideoId;

  // Track definitions
  final List<TrackInfo> _tracks = [
    TrackInfo(
        'Video', MediaType.video, AppConstants.videoTrackColor, Icons.videocam),
    TrackInfo('Audio', MediaType.audio, AppConstants.audioTrackColor,
        Icons.audiotrack),
    TrackInfo(
        'Image', MediaType.image, AppConstants.imageTrackColor, Icons.image),
    TrackInfo(
        'Text', MediaType.text, AppConstants.textTrackColor, Icons.text_fields),
  ];

  @override
  void initState() {
    super.initState();

    try {
      _cutAnimationController = AnimationController(
        duration: const Duration(milliseconds: 1500),
        vsync: this,
      );
      _cutAnimation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _cutAnimationController,
        curve: Curves.easeInOut,
      ));

      // Start animation when in cut mode
      if (widget.isCutMode) {
        _cutAnimationController.repeat(reverse: true);
      }
    } catch (e) {
      // Handle vendor-specific media property errors (OnePlus/OPPO)
      if (e.toString().contains('vendor.oplus.media') ||
          e.toString().contains('vendor.oppo.media') ||
          e.toString().contains('vendor.oplus') ||
          e.toString().contains('Access denied finding property')) {
        // Ignore vendor-specific property access errors - these are safe to ignore
        debugPrint('Vendor media property access denied (safe to ignore): $e');

        // Create a fallback animation controller
        try {
          _cutAnimationController = AnimationController(
            duration: const Duration(milliseconds: 1500),
            vsync: this,
          );
          _cutAnimation = Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(_cutAnimationController);
        } catch (fallbackError) {
          // If even fallback fails, create minimal controller
          _cutAnimationController = AnimationController(
            duration: const Duration(milliseconds: 1500),
            vsync: this,
          );
          _cutAnimation = AlwaysStoppedAnimation(0.0);
        }
      } else {
        // Re-throw other errors
        rethrow;
      }
    }
  }

  @override
  void didUpdateWidget(ProfessionalTimeline oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle cut mode animation
    if (widget.isCutMode && !oldWidget.isCutMode) {
      _cutAnimationController.repeat(reverse: true);
    } else if (!widget.isCutMode && oldWidget.isCutMode) {
      _cutAnimationController.stop();
      _cutAnimationController.reset();
    }

    // Check for video transitions when position changes
    if (widget.currentPosition != oldWidget.currentPosition) {
      _checkVideoTransition(oldWidget.currentPosition, widget.currentPosition);
    }
  }

  /// Check for video transitions when timeline position changes
  void _checkVideoTransition(double oldPosition, double newPosition) {
    if (widget.onVideoTransition == null) return;

    // Get video items sorted by layer (highest layer = topmost)
    final videoItems = widget.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .toList()
      ..sort((a, b) => b.layer.compareTo(a.layer));

    if (videoItems.isEmpty) return;

    // Find active video at old position
    final oldActiveVideo = videoItems.firstWhere(
      (item) => oldPosition >= item.startTime && oldPosition <= item.endTime,
      orElse: () => videoItems.first,
    );

    // Find active video at new position
    final newActiveVideo = videoItems.firstWhere(
      (item) => newPosition >= item.startTime && newPosition <= item.endTime,
      orElse: () => videoItems.first,
    );

    // Check if we've transitioned to a different video
    if (oldActiveVideo.id != newActiveVideo.id) {
      debugPrint(
          'Video transition detected: ${oldActiveVideo.id} -> ${newActiveVideo.id}');
      _currentActiveVideoId = newActiveVideo.id;
      widget.onVideoTransition!(newPosition);
    }

    // Also check if we've crossed video boundaries (for continuous playback)
    _checkVideoBoundaries(oldPosition, newPosition, videoItems);
  }

  /// Check if playhead has crossed video boundaries for continuous playback
  void _checkVideoBoundaries(
      double oldPosition, double newPosition, List<MediaItem> videoItems) {
    if (widget.onVideoTransition == null) return;

    // Check if we're moving forward and crossing video end boundaries
    if (newPosition > oldPosition) {
      for (final video in videoItems) {
        // Check if we crossed the end of this video
        if (oldPosition < video.endTime && newPosition >= video.endTime) {
          // Find next video that should start
          final nextVideo = videoItems.firstWhere(
            (item) =>
                item.startTime >= video.endTime &&
                newPosition >= item.startTime &&
                newPosition <= item.endTime,
            orElse: () => video,
          );

          if (nextVideo.id != video.id) {
            debugPrint(
                'Video boundary crossed: ${video.id} -> ${nextVideo.id} at ${newPosition}s');
            _currentActiveVideoId = nextVideo.id;
            widget.onVideoTransition!(newPosition);
            break;
          }
        }
      }
    }
  }

  @override
  void dispose() {
    _cutAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: Container(
        height: AppConstants.timelineHeight,
        decoration: BoxDecoration(
          color: Color(AppConstants.timelineBackground),
          border: Border(
            top: BorderSide(
                color: Colors.white.withValues(alpha: 0.1), width: 1),
          ),
        ),
        child: Column(
          children: [
            // Zoom controls
            _buildZoomControls(),
            // Time ruler
            _buildTimeRuler(),
            // Timeline tracks
            Expanded(
              child: Row(
                children: [
                  // Track labels
                  _buildTrackLabels(),
                  // Timeline content with horizontal scroll
                  Expanded(
                    child: _buildScrollableTimelineContent(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRuler() {
    return Container(
      height: AppConstants.timeRulerHeight,
      decoration: BoxDecoration(
        color: Color(AppConstants.surfaceColor),
        border: Border(
          bottom:
              BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Spacer for track labels
          SizedBox(width: AppConstants.trackLabelWidth),
          // Time ruler content
          Expanded(
            child: widget.isCutMode &&
                    widget.trimStart != null &&
                    widget.trimEnd != null
                ? _buildSplitTimeRuler()
                : CustomPaint(
                    painter: TimeRulerPainter(
                      duration: widget.duration,
                      currentPosition:
                          _isDragging ? _dragPosition : widget.currentPosition,
                      zoomLevel: _zoomLevel,
                    ),
                    size: Size.infinite,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSplitTimeRuler() {
    final cutStart = widget.trimStart!;
    final cutEnd = widget.trimEnd!;
    final gapWidth = 40.0;

    return Stack(
      children: [
        // Left ruler segment
        Positioned(
          left: 0,
          top: 0,
          bottom: 0,
          width: _getPositionX(cutStart),
          child: CustomPaint(
            painter: SplitTimeRulerPainter(
              startTime: 0.0,
              endTime: cutStart,
              currentPosition:
                  _isDragging ? _dragPosition : widget.currentPosition,
              zoomLevel: _zoomLevel,
              isLeftSegment: true,
            ),
            size: Size.infinite,
          ),
        ),

        // Right ruler segment
        Positioned(
          left: _getPositionX(cutStart) + gapWidth,
          top: 0,
          bottom: 0,
          width: _getPositionX(widget.duration - cutEnd),
          child: CustomPaint(
            painter: SplitTimeRulerPainter(
              startTime: cutEnd,
              endTime: widget.duration,
              currentPosition:
                  _isDragging ? _dragPosition : widget.currentPosition,
              zoomLevel: _zoomLevel,
              isRightSegment: true,
              timeOffset: cutEnd - cutStart,
            ),
            size: Size.infinite,
          ),
        ),

        // Gap in ruler
        Positioned(
          left: _getPositionX(cutStart),
          top: 0,
          bottom: 0,
          width: gapWidth,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.2),
              border: Border.symmetric(
                vertical: BorderSide(color: Colors.red, width: 1),
              ),
            ),
            child: const Center(
              child: Text(
                'CUT',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 8,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTrackLabels() {
    return Container(
      width: AppConstants.trackLabelWidth,
      decoration: BoxDecoration(
        color: Color(AppConstants.surfaceColor),
        border: Border(
          right:
              BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: Column(
        children: _tracks.map((track) => _buildTrackLabel(track)).toList(),
      ),
    );
  }

  Widget _buildTrackLabel(TrackInfo track) {
    return Container(
      height: AppConstants.trackHeight,
      decoration: BoxDecoration(
        border: Border(
          bottom:
              BorderSide(color: Colors.white.withValues(alpha: 0.05), width: 1),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 12),
          Icon(
            track.icon,
            color: Color(track.color),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              track.name,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildZoomControls() {
    return Container(
      height: 30,
      decoration: BoxDecoration(
        color: Color(AppConstants.surfaceColor),
        border: Border(
          bottom:
              BorderSide(color: Colors.white.withValues(alpha: 0.05), width: 1),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 12),
          Text(
            'Zoom:',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 8),
          // Zoom out button
          IconButton(
            onPressed: _zoomLevel > 0.25 ? _zoomOut : null,
            icon: const Icon(Icons.zoom_out, size: 16),
            color: Colors.white.withValues(alpha: 0.7),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            padding: EdgeInsets.zero,
          ),
          // Zoom slider
          Expanded(
            child: Slider(
              value: _zoomLevel,
              min: 0.25,
              max: 4.0,
              divisions: 15,
              activeColor: Color(AppConstants.accentColor),
              inactiveColor: Colors.white.withValues(alpha: 0.2),
              onChanged: (value) {
                setState(() {
                  _zoomLevel = value;
                });
              },
            ),
          ),
          // Zoom in button
          IconButton(
            onPressed: _zoomLevel < 4.0 ? _zoomIn : null,
            icon: const Icon(Icons.zoom_in, size: 16),
            color: Colors.white.withValues(alpha: 0.7),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            padding: EdgeInsets.zero,
          ),
          // Zoom level indicator with tooltip
          Tooltip(
            message:
                'Keyboard shortcuts:\nCtrl + = : Zoom In\nCtrl + - : Zoom Out\nCtrl + 0 : Reset Zoom',
            child: Container(
              width: 50,
              alignment: Alignment.center,
              child: Text(
                '${(_zoomLevel * 100).round()}%',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
        ],
      ),
    );
  }

  Widget _buildScrollableTimelineContent() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: _getTimelineWidth(),
        child: _buildTimelineContent(),
      ),
    );
  }

  Widget _buildTimelineContent() {
    return Container(
      decoration: BoxDecoration(
        color: Color(AppConstants.trackBackground),
      ),
      child: GestureDetector(
        onTapDown: _onTimelineClick,
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: _buildNormalTimelineContent(),
      ),
    );
  }

  Widget _buildNormalTimelineContent() {
    return Stack(
      children: [
        // Background grid
        CustomPaint(
          painter: TimelineGridPainter(
            duration: widget.duration,
            zoomLevel: _zoomLevel,
            trackCount: _tracks.length,
          ),
          size: Size.infinite,
        ),
        // Media items
        ...widget.mediaItems.map((item) => _buildMediaItemWidget(item)),
        // Trim/Cut indicators
        if (widget.isTrimMode || widget.isCutMode) _buildTrimCutIndicators(),
        // Playhead
        _buildPlayhead(),

        // Simple cut indicator (just shows current position as cut point)
        if (widget.isCutMode) _buildCutPositionIndicator(),
      ],
    );
  }

  // Removed complex split timeline - using simple cut approach

  Widget _buildMediaItemWidget(MediaItem item) {
    final trackIndex = _getTrackIndex(item.type);
    if (trackIndex == -1) return const SizedBox.shrink();

    final startX = _getPositionX(item.startTime);
    final width = _getPositionX(item.endTime - item.startTime)
        .clamp(20.0, double.infinity);
    final top = trackIndex * AppConstants.trackHeight;

    return Positioned(
      left: startX,
      top: top + 4,
      child: SizedBox(
        width: width,
        height: AppConstants.trackHeight - 8,
        child: GestureDetector(
          onPanStart: (details) => _onMediaItemPanStart(details, item),
          onPanUpdate: (details) => _onMediaItemPanUpdate(details, item),
          onPanEnd: (details) => _onMediaItemPanEnd(details, item),
          onTap: () => widget.onMediaItemTap(item),
          child: _buildMediaClip(item),
        ),
      ),
    );
  }

  Widget _buildMediaClip(MediaItem item) {
    final track = _tracks.firstWhere((t) => t.type == item.type);
    final isSelected = _draggingItem?.id == item.id;

    return Container(
      decoration: BoxDecoration(
        color: Color(track.color).withValues(alpha: isSelected ? 1.0 : 0.8),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isSelected ? Colors.white : Color(track.color),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      clipBehavior: Clip.hardEdge, // Prevent content overflow
      child: Stack(
        clipBehavior: Clip.hardEdge, // Prevent stack children overflow
        children: [
          // Main content
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Calculate available width with safety margin
                final availableWidth =
                    constraints.maxWidth - 4; // 4px safety margin
                final iconWidth = 10.0;
                final spacingWidth = 3.0;
                final minTextWidth = 20.0; // Minimum text width
                final textWidth = availableWidth - iconWidth - spacingWidth;

                // If space is too tight, show icon only
                if (availableWidth < iconWidth + spacingWidth + minTextWidth) {
                  return SizedBox(
                    width: availableWidth.clamp(iconWidth, double.infinity),
                    child: Center(
                      child: Icon(
                        track.icon,
                        color: Colors.white,
                        size: 8, // Smaller icon for tight spaces
                      ),
                    ),
                  );
                }

                return SizedBox(
                  width: availableWidth,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: iconWidth,
                        child: Icon(
                          track.icon,
                          color: Colors.white,
                          size: 10,
                        ),
                      ),
                      SizedBox(width: spacingWidth),
                      Expanded(
                        child: Text(
                          _getMediaItemLabel(item),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8, // Slightly smaller font
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      // Delete button (visible when item is wide enough)
                      if (availableWidth > 60)
                        GestureDetector(
                          onTap: () => _showDeleteConfirmation(item),
                          child: Container(
                            margin: const EdgeInsets.only(left: 2),
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.8),
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 8,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
          // Resize handles
          if (isSelected) ...[
            // Left resize handle
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onPanStart: (details) => _onResizeStart(details, item, true),
                onPanUpdate: (details) => _onResizeUpdate(details, item, true),
                onPanEnd: (details) => _onResizeEnd(details, item),
                child: Container(
                  width: 8,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.8),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(4),
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.drag_indicator,
                      size: 8,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
            // Right resize handle
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onPanStart: (details) => _onResizeStart(details, item, false),
                onPanUpdate: (details) => _onResizeUpdate(details, item, false),
                onPanEnd: (details) => _onResizeEnd(details, item),
                child: Container(
                  width: 8,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.8),
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.drag_indicator,
                      size: 8,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTrimCutIndicators() {
    return Stack(
      children: [
        // Start trim/cut indicator
        if (widget.trimStart != null)
          Positioned(
            left: _getPositionX(widget.trimStart!) - 2,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onPanUpdate: (details) {
                if (widget.onTrimStartChanged != null) {
                  final deltaX = details.delta.dx;
                  final deltaTime =
                      (deltaX / _getTimelineWidth()) * widget.duration;
                  final newTime = (widget.trimStart! + deltaTime)
                      .clamp(0.0, widget.trimEnd ?? widget.duration);
                  widget.onTrimStartChanged!(newTime);
                }
              },
              child: Container(
                width: 4,
                decoration: BoxDecoration(
                  color: widget.isTrimMode
                      ? Color(AppConstants.accentColor)
                      : Colors.red,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: widget.isTrimMode
                            ? Color(AppConstants.accentColor)
                            : Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        widget.isTrimMode
                            ? Icons.content_cut
                            : Icons.call_split,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        width: 4,
                        color: widget.isTrimMode
                            ? Color(AppConstants.accentColor)
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        // End trim/cut indicator
        if (widget.trimEnd != null)
          Positioned(
            left: _getPositionX(widget.trimEnd!) - 2,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onPanUpdate: (details) {
                if (widget.onTrimEndChanged != null) {
                  final deltaX = details.delta.dx;
                  final deltaTime =
                      (deltaX / _getTimelineWidth()) * widget.duration;
                  final newTime = (widget.trimEnd! + deltaTime)
                      .clamp(widget.trimStart ?? 0.0, widget.duration);
                  widget.onTrimEndChanged!(newTime);
                }
              },
              child: Container(
                width: 4,
                decoration: BoxDecoration(
                  color: widget.isTrimMode
                      ? Color(AppConstants.accentColor)
                      : Colors.red,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: widget.isTrimMode
                            ? Color(AppConstants.accentColor)
                            : Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        widget.isTrimMode
                            ? Icons.content_cut
                            : Icons.call_split,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                    Expanded(
                      child: Container(
                        width: 4,
                        color: widget.isTrimMode
                            ? Color(AppConstants.accentColor)
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        // Trim/Cut area overlay
        if (widget.trimStart != null && widget.trimEnd != null) ...[
          if (widget.isTrimMode)
            // Trim mode: highlight the kept area
            Positioned(
              left: _getPositionX(widget.trimStart!),
              top: 0,
              bottom: 0,
              width: _getPositionX(widget.trimEnd! - widget.trimStart!),
              child: Container(
                decoration: BoxDecoration(
                  color: Color(AppConstants.accentColor).withValues(alpha: 0.1),
                  border: Border.symmetric(
                    vertical: BorderSide(
                      color: Color(AppConstants.accentColor),
                      width: 1,
                    ),
                  ),
                ),
              ),
            )
          // Cut mode is handled by _buildSplitTimelineContent(), no overlay needed
        ],
      ],
    );
  }

  // Removed _buildCutModeOverlay - using _buildSplitTimelineContent instead

  // Removed unused methods _buildTimelineSegment and _buildGapIndicator

  // Simple cut position indicator
  Widget _buildCutPositionIndicator() {
    final x = _getPositionX(widget.currentPosition);
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate safe positioning to prevent overflow
    double leftPosition = x - 20;

    // Ensure the indicator doesn't overflow the screen
    if (leftPosition < 0) {
      leftPosition = 0;
    } else if (leftPosition + 80 > screenWidth) {
      // 80 is approximate width of indicator
      leftPosition = screenWidth - 80;
    }

    return Positioned(
      left: leftPosition,
      top: -10,
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 80, // Limit maximum width to prevent overflow
          minWidth: 60,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: IntrinsicWidth(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.call_split,
                color: Colors.white,
                size: 10,
              ),
              const SizedBox(width: 3),
              Flexible(
                child: Text(
                  'CUT',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 9,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Removed complex split playhead methods - using simple approach

  Widget _buildPlayhead() {
    final position = _isDragging ? _dragPosition : widget.currentPosition;
    final x = _getPositionX(position);

    return Positioned(
      left: x - 1,
      top: 0,
      bottom: 0,
      child: Container(
        width: 2,
        color: Color(AppConstants.playheadColor),
        child: Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: Color(AppConstants.playheadColor),
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                width: 2,
                color: Color(AppConstants.playheadColor),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  double _getPositionX(double time) {
    if (widget.duration <= 0) return 0;
    return (time / widget.duration) * _getTimelineWidth() * _zoomLevel;
  }

  double _getTimelineWidth() {
    final baseWidth =
        MediaQuery.of(context).size.width - AppConstants.trackLabelWidth;
    return baseWidth * _zoomLevel;
  }

  // Removed unused helper methods for split timeline

  int _getTrackIndex(MediaType type) {
    return _tracks.indexWhere((track) => track.type == type);
  }

  String _getMediaItemLabel(MediaItem item) {
    switch (item.type) {
      case MediaType.text:
        return item.text ?? 'Text';
      case MediaType.video:
      case MediaType.audio:
      case MediaType.image:
        return item.filePath?.split('/').last ?? item.type.name;
    }
  }

  void _showDeleteConfirmation(MediaItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Color(AppConstants.surfaceColor),
        title: const Text(
          'Delete Media Item',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this ${item.type.name} item?',
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getTrackColor(item.type).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: _getTrackColor(item.type),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getTrackIcon(item.type),
                    size: 16,
                    color: _getTrackColor(item.type),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getMediaItemLabel(item),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onMediaItemDelete(item);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getTrackColor(MediaType type) {
    final track = _tracks.firstWhere((t) => t.type == type);
    return Color(track.color);
  }

  IconData _getTrackIcon(MediaType type) {
    final track = _tracks.firstWhere((t) => t.type == type);
    return track.icon;
  }

  // Zoom methods
  void _zoomIn() {
    setState(() {
      _zoomLevel = (_zoomLevel * 1.25).clamp(0.25, 4.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = (_zoomLevel / 1.25).clamp(0.25, 4.0);
    });
  }

  // Event handlers
  void _onTimelineClick(TapDownDetails details) {
    final x = details.localPosition.dx;
    final time = (x / (_getTimelineWidth() * _zoomLevel)) * widget.duration;
    widget.onPositionChanged(time.clamp(0, widget.duration));
  }

  void _onPanStart(DragStartDetails details) {
    _isDragging = true;
    final x = details.localPosition.dx;
    _dragPosition = (x / (_getTimelineWidth() * _zoomLevel)) * widget.duration;
    setState(() {});
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final x = details.localPosition.dx;
    _dragPosition = (x / (_getTimelineWidth() * _zoomLevel)) * widget.duration;
    _dragPosition = _dragPosition.clamp(0, widget.duration);
    setState(() {});
  }

  void _onPanEnd(DragEndDetails details) {
    _isDragging = false;
    widget.onPositionChanged(_dragPosition);
    setState(() {});
  }

  // Media item drag handlers
  void _onMediaItemPanStart(DragStartDetails details, MediaItem item) {
    _draggingItem = item;
    _isDraggingItem = true;
    _dragStartX = details.localPosition.dx;
    _originalStartTime = item.startTime;
    _originalEndTime = item.endTime;
    setState(() {});
  }

  void _onMediaItemPanUpdate(DragUpdateDetails details, MediaItem item) {
    if (!_isDraggingItem || _draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = (deltaX / _getTimelineWidth()) * widget.duration;

    final newStartTime =
        (_originalStartTime + deltaTime).clamp(0.0, widget.duration);

    // Update the item position in real-time
    widget.onMediaItemMove(item, newStartTime);
  }

  void _onMediaItemPanEnd(DragEndDetails details, MediaItem item) {
    if (!_isDraggingItem || _draggingItem?.id != item.id) return;

    _draggingItem = null;
    _isDraggingItem = false;
    _dragStartX = 0;
    _originalStartTime = 0;
    _originalEndTime = 0;
    setState(() {});
  }

  // Resize handlers
  void _onResizeStart(
      DragStartDetails details, MediaItem item, bool isStartHandle) {
    _resizingItem = item;
    _isResizingStart = isStartHandle;
    _isResizingEnd = !isStartHandle;
    _dragStartX = details.localPosition.dx;
    _originalStartTime = item.startTime;
    _originalEndTime = item.endTime;
    setState(() {});
  }

  void _onResizeUpdate(
      DragUpdateDetails details, MediaItem item, bool isStartHandle) {
    if (_resizingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = (deltaX / _getTimelineWidth()) * widget.duration;

    if (isStartHandle) {
      // Resizing start time
      final newStartTime =
          (_originalStartTime + deltaTime).clamp(0.0, _originalEndTime - 0.1);
      widget.onMediaItemResize(item, newStartTime, _originalEndTime);
    } else {
      // Resizing end time
      final newEndTime = (_originalEndTime + deltaTime)
          .clamp(_originalStartTime + 0.1, widget.duration);
      widget.onMediaItemResize(item, _originalStartTime, newEndTime);
    }
  }

  void _onResizeEnd(DragEndDetails details, MediaItem item) {
    _resizingItem = null;
    _isResizingStart = false;
    _isResizingEnd = false;
    _dragStartX = 0;
    _originalStartTime = 0;
    _originalEndTime = 0;
    setState(() {});
  }

  // Keyboard shortcuts
  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      if (HardwareKeyboard.instance.isControlPressed) {
        switch (event.logicalKey) {
          case LogicalKeyboardKey.equal:
          case LogicalKeyboardKey.add:
            if (_zoomLevel < 4.0) {
              _zoomIn();
              return KeyEventResult.handled;
            }
            break;
          case LogicalKeyboardKey.minus:
            if (_zoomLevel > 0.25) {
              _zoomOut();
              return KeyEventResult.handled;
            }
            break;
          case LogicalKeyboardKey.digit0:
            setState(() {
              _zoomLevel = 1.0; // Reset to 100%
            });
            return KeyEventResult.handled;
        }
      }
    }
    return KeyEventResult.ignored;
  }
}

// Data classes
class TrackInfo {
  final String name;
  final MediaType type;
  final int color;
  final IconData icon;

  TrackInfo(this.name, this.type, this.color, this.icon);
}

// Custom painters
class TimeRulerPainter extends CustomPainter {
  final double duration;
  final double currentPosition;
  final double zoomLevel;

  TimeRulerPainter({
    required this.duration,
    required this.currentPosition,
    required this.zoomLevel,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (duration <= 0 || size.width <= 0) return;

    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6)
      ..strokeWidth = 1;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Calculate time intervals based on zoom level
    double interval = _calculateInterval();
    final pixelsPerSecond = (size.width / duration) * zoomLevel;

    // Draw time markers
    for (double time = 0; time <= duration; time += interval) {
      final x = time * pixelsPerSecond;
      if (x > size.width) break;

      // Draw tick mark
      canvas.drawLine(
        Offset(x, size.height - 10),
        Offset(x, size.height),
        paint,
      );

      // Draw time label
      final timeText = _formatTime(time);
      textPainter.text = TextSpan(
        text: timeText,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.8),
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x + 2, 2));
    }
  }

  double _calculateInterval() {
    if (zoomLevel >= 2.0) return 1.0; // 1 second
    if (zoomLevel >= 1.0) return 5.0; // 5 seconds
    if (zoomLevel >= 0.5) return 10.0; // 10 seconds
    return 30.0; // 30 seconds
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final secs = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class TimelineGridPainter extends CustomPainter {
  final double duration;
  final double zoomLevel;
  final int trackCount;

  TimelineGridPainter({
    required this.duration,
    required this.zoomLevel,
    required this.trackCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (duration <= 0 || size.width <= 0) return;

    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.05)
      ..strokeWidth = 0.5;

    // Draw horizontal lines (track separators)
    for (int i = 1; i < trackCount; i++) {
      final y = i * AppConstants.trackHeight;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical grid lines
    final interval = _calculateGridInterval();
    final pixelsPerSecond = (size.width / duration) * zoomLevel;

    for (double time = 0; time <= duration; time += interval) {
      final x = time * pixelsPerSecond;
      if (x > size.width) break;

      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  double _calculateGridInterval() {
    if (zoomLevel >= 2.0) return 1.0;
    if (zoomLevel >= 1.0) return 5.0;
    if (zoomLevel >= 0.5) return 10.0;
    return 30.0;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Split time ruler painter for cut mode
class SplitTimeRulerPainter extends CustomPainter {
  final double startTime;
  final double endTime;
  final double currentPosition;
  final double zoomLevel;
  final bool isLeftSegment;
  final bool isRightSegment;
  final double timeOffset;

  SplitTimeRulerPainter({
    required this.startTime,
    required this.endTime,
    required this.currentPosition,
    required this.zoomLevel,
    this.isLeftSegment = false,
    this.isRightSegment = false,
    this.timeOffset = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (endTime <= startTime || size.width <= 0) return;

    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6)
      ..strokeWidth = 1;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Calculate time intervals based on segment duration
    final segmentDuration = endTime - startTime;
    double interval = _calculateInterval(segmentDuration);
    final pixelsPerSecond = (size.width / segmentDuration) * zoomLevel;

    // Draw time markers for this segment
    for (double time = 0; time <= segmentDuration; time += interval) {
      final x = time * pixelsPerSecond;
      if (x > size.width) break;

      // Draw tick mark
      canvas.drawLine(
        Offset(x, size.height - 10),
        Offset(x, size.height),
        paint,
      );

      // Calculate display time
      double displayTime;
      if (isRightSegment) {
        // For right segment, show adjusted time (subtract cut duration)
        displayTime = startTime + time - timeOffset;
      } else {
        // For left segment, show normal time
        displayTime = startTime + time;
      }

      // Draw time label
      final timeText = _formatTime(displayTime);
      textPainter.text = TextSpan(
        text: timeText,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.8),
          fontSize: 10,
        ),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x + 2, 2));
    }
  }

  double _calculateInterval(double duration) {
    if (duration <= 10) return 1.0;
    if (duration <= 60) return 5.0;
    if (duration <= 300) return 10.0;
    return 30.0;
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final secs = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Segment grid painter for split timeline
class SegmentGridPainter extends CustomPainter {
  final double startTime;
  final double endTime;
  final double zoomLevel;
  final int trackCount;
  final double timeOffset;

  SegmentGridPainter({
    required this.startTime,
    required this.endTime,
    required this.zoomLevel,
    required this.trackCount,
    this.timeOffset = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (endTime <= startTime || size.width <= 0) return;

    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.05)
      ..strokeWidth = 0.5;

    // Draw horizontal lines (track separators)
    for (int i = 1; i < trackCount; i++) {
      final y = i * AppConstants.trackHeight;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical grid lines
    final segmentDuration = endTime - startTime;
    final interval = _calculateGridInterval(segmentDuration);
    final pixelsPerSecond = (size.width / segmentDuration) * zoomLevel;

    for (double time = 0; time <= segmentDuration; time += interval) {
      final x = time * pixelsPerSecond;
      if (x > size.width) break;

      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  double _calculateGridInterval(double duration) {
    if (duration <= 10) return 1.0;
    if (duration <= 60) return 5.0;
    if (duration <= 300) return 10.0;
    return 30.0;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Diagonal lines painter for visual effects
class DiagonalLinesPainter extends CustomPainter {
  final Color color;
  final double spacing;
  final bool isAnimated;

  DiagonalLinesPainter({
    required this.color,
    required this.spacing,
    this.isAnimated = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw diagonal lines
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => isAnimated;
}

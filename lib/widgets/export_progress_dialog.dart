import 'package:flutter/material.dart';
import '../utils/constants.dart';

class ExportProgressDialog extends StatefulWidget {
  final String projectName;
  final Function(Function(double) onProgress, Function(String) onStatus, Function(String) onCompleted, Function(String) onError) onExport;
  final VoidCallback onCancel;

  const ExportProgressDialog({
    super.key,
    required this.projectName,
    required this.onExport,
    required this.onCancel,
  });

  @override
  State<ExportProgressDialog> createState() => _ExportProgressDialogState();
}

class _ExportProgressDialogState extends State<ExportProgressDialog>
    with TickerProviderStateMixin {
  double _progress = 0.0;
  String _statusMessage = 'Preparing export...';
  bool _isExporting = false;
  bool _isCompleted = false;
  bool _hasError = false;
  String? _outputPath;
  String? _errorMessage;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void updateProgress(double progress) {
    if (mounted) {
      setState(() {
        _progress = progress;
      });
    }
  }

  void updateStatus(String message) {
    if (mounted) {
      setState(() {
        _statusMessage = message;
      });
    }
  }

  void setCompleted(String outputPath) {
    if (mounted) {
      setState(() {
        _isCompleted = true;
        _isExporting = false;
        _outputPath = outputPath;
        _progress = 1.0;
        _statusMessage = 'Export completed successfully!';
      });
      _pulseController.stop();
    }
  }

  void setError(String error) {
    if (mounted) {
      setState(() {
        _hasError = true;
        _isExporting = false;
        _errorMessage = error;
        _statusMessage = 'Export failed';
      });
      _pulseController.stop();
    }
  }

  void _startExport() {
    setState(() {
      _isExporting = true;
      _hasError = false;
      _isCompleted = false;
      _progress = 0.0;
      _statusMessage = 'Starting export...';
    });

    widget.onExport(
      updateProgress,
      updateStatus,
      setCompleted,
      setError,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isExporting ? _pulseAnimation.value : 1.0,
                      child: Icon(
                        _isCompleted
                            ? Icons.check_circle
                            : _hasError
                                ? Icons.error
                                : Icons.video_file,
                        color: _isCompleted
                            ? Colors.green
                            : _hasError
                                ? Colors.red
                                : Color(AppConstants.accentColor),
                        size: 32,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Export Video',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.projectName,
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Progress Section
            if (_isExporting || _isCompleted || _hasError) ...[
              // Progress Bar
              Container(
                width: double.infinity,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.white10,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: _progress,
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _hasError
                          ? Colors.red
                          : _isCompleted
                              ? Colors.green
                              : Color(AppConstants.accentColor),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Progress Text
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _statusMessage,
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    '${(_progress * 100).toInt()}%',
                    style: TextStyle(
                      color: Color(AppConstants.accentColor),
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Error Message
            if (_hasError && _errorMessage != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                    if (_errorMessage!.contains('permission')) ...[
                      const SizedBox(height: 8),
                      const Text(
                        'Tip: Go to Settings > Apps > Video Editor > Permissions and enable Storage access.',
                        style: TextStyle(
                          color: Colors.orange,
                          fontSize: 11,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Success Message
            if (_isCompleted && _outputPath != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Video exported to Downloads/VideoEditor:',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _outputPath!.split('/').last.split('\\').last,
                      style: const TextStyle(
                        color: Colors.green,
                        fontSize: 11,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '📁 Additional files created:',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      '• .timeline.txt - Timeline structure\n• .preview.json - Preview metadata',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (!_isExporting) ...[
                  TextButton(
                    onPressed: widget.onCancel,
                    child: Text(
                      _isCompleted ? 'Close' : 'Cancel',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],

                if (!_isExporting && !_isCompleted && !_hasError)
                  ElevatedButton(
                    onPressed: _startExport,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(AppConstants.accentColor),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Start Export'),
                  ),

                if (_isCompleted)
                  ElevatedButton(
                    onPressed: () {
                      // Open Downloads folder
                      widget.onCancel();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Open Folder'),
                  ),

                if (_hasError)
                  ElevatedButton(
                    onPressed: _startExport,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(AppConstants.accentColor),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

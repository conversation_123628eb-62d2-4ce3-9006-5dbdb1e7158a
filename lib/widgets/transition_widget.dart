import 'package:flutter/material.dart';
import '../models/video_project.dart';
import '../utils/constants.dart';

class TransitionWidget extends StatefulWidget {
  final TransitionSettings? currentTransition;
  final Function(TransitionSettings) onTransitionChanged;
  final VoidCallback onCancel;

  const TransitionWidget({
    super.key,
    this.currentTransition,
    required this.onTransitionChanged,
    required this.onCancel,
  });

  @override
  State<TransitionWidget> createState() => _TransitionWidgetState();
}

class _TransitionWidgetState extends State<TransitionWidget> {
  late TransitionType _selectedType;
  late double _duration;
  late double _intensity;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.currentTransition?.type ?? TransitionType.fade;
    _duration = widget.currentTransition?.duration ?? 1.0;
    _intensity = widget.currentTransition?.intensity ?? 1.0;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        width: screenWidth > 600 ? 500 : screenWidth * 0.9,
        height: screenHeight > 700 ? 600 : screenHeight * 0.85,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.slideshow,
                  color: Color(AppConstants.accentColor),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Transition Settings',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close, color: Colors.white70),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Transition Type Selection
                    Text(
                      'Transition Type',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 140,
                      child: GridView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: screenWidth > 500 ? 5 : 3,
                          childAspectRatio: 1.1,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: TransitionType.values.length,
                        itemBuilder: (context, index) {
                  final type = TransitionType.values[index];
                  final isSelected = type == _selectedType;

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedType = type;
                      });
                      _applyTransitionImmediately();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Color(AppConstants.accentColor).withValues(alpha: 0.3)
                            : Colors.white10,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? Color(AppConstants.accentColor)
                              : Colors.white30,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getTransitionIcon(type),
                            color: isSelected
                                ? Color(AppConstants.accentColor)
                                : Colors.white70,
                            size: 20,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getTransitionName(type),
                            style: TextStyle(
                              color: isSelected
                                  ? Color(AppConstants.accentColor)
                                  : Colors.white70,
                              fontSize: 10,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 20),

            // Duration Setting
            Text(
              'Duration: ${_duration.toStringAsFixed(1)}s',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Slider(
              value: _duration,
              min: 0.1,
              max: 5.0,
              divisions: 49,
              activeColor: Color(AppConstants.accentColor),
              inactiveColor: Colors.white30,
              onChanged: (value) {
                setState(() {
                  _duration = value;
                });
                _applyTransitionImmediately();
              },
            ),
            const SizedBox(height: 16),

            // Intensity Setting
            Text(
              'Intensity: ${(_intensity * 100).round()}%',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Slider(
              value: _intensity,
              min: 0.1,
              max: 2.0,
              divisions: 19,
              activeColor: Color(AppConstants.accentColor),
              inactiveColor: Colors.white30,
              onChanged: (value) {
                setState(() {
                  _intensity = value;
                });
                _applyTransitionImmediately();
              },
            ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Close Button
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.accentColor),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: const Text('Done'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTransitionIcon(TransitionType type) {
    switch (type) {
      case TransitionType.none:
        return Icons.block;
      case TransitionType.fade:
        return Icons.gradient;
      case TransitionType.slideLeft:
        return Icons.keyboard_arrow_left;
      case TransitionType.slideRight:
        return Icons.keyboard_arrow_right;
      case TransitionType.slideUp:
        return Icons.keyboard_arrow_up;
      case TransitionType.slideDown:
        return Icons.keyboard_arrow_down;
      case TransitionType.zoom:
        return Icons.zoom_in;
      case TransitionType.dissolve:
        return Icons.blur_on;
      case TransitionType.wipe:
        return Icons.swipe;
      case TransitionType.circle:
        return Icons.circle;
    }
  }

  String _getTransitionName(TransitionType type) {
    switch (type) {
      case TransitionType.none:
        return 'None';
      case TransitionType.fade:
        return 'Fade';
      case TransitionType.slideLeft:
        return 'Slide Left';
      case TransitionType.slideRight:
        return 'Slide Right';
      case TransitionType.slideUp:
        return 'Slide Up';
      case TransitionType.slideDown:
        return 'Slide Down';
      case TransitionType.zoom:
        return 'Zoom';
      case TransitionType.dissolve:
        return 'Dissolve';
      case TransitionType.wipe:
        return 'Wipe';
      case TransitionType.circle:
        return 'Circle';
    }
  }

  void _applyTransitionImmediately() {
    final transitionSettings = TransitionSettings(
      type: _selectedType,
      duration: _duration,
      intensity: _intensity,
    );

    widget.onTransitionChanged(transitionSettings);
  }
}

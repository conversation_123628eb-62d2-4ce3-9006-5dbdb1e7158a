import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/video_project.dart';
import '../services/temporary_video_service.dart';
import '../utils/constants.dart';

/// Enhanced timeline widget with temporary editing support
class EnhancedTimeline extends StatefulWidget {
  final double duration;
  final double currentPosition;
  final List<MediaItem> mediaItems;
  final Function(double) onPositionChanged;
  final Function(MediaItem) onMediaItemTap;
  final Function(MediaItem) onMediaItemDelete;
  final Function(MediaItem, double, double) onMediaItemResize;
  final Function(MediaItem, double) onMediaItemMove;
  final Function(double)? onVideoTransition;

  // Enhanced trim/cut functionality
  final bool isTrimMode;
  final bool isCutMode;
  final double? trimStart;
  final double? trimEnd;
  final Function(double)? onTrimStartChanged;
  final Function(double)? onTrimEndChanged;
  final Function()? onApplyTemporaryTrim;
  final Function()? onApplyTemporaryCut;
  final Function()? onResetTemporaryEdits;
  final Function()? onPreviewTemporaryEdits;

  const EnhancedTimeline({
    super.key,
    required this.duration,
    required this.currentPosition,
    required this.mediaItems,
    required this.onPositionChanged,
    required this.onMediaItemTap,
    required this.onMediaItemDelete,
    required this.onMediaItemResize,
    required this.onMediaItemMove,
    this.onVideoTransition,
    this.isTrimMode = false,
    this.isCutMode = false,
    this.trimStart,
    this.trimEnd,
    this.onTrimStartChanged,
    this.onTrimEndChanged,
    this.onApplyTemporaryTrim,
    this.onApplyTemporaryCut,
    this.onResetTemporaryEdits,
    this.onPreviewTemporaryEdits,
  });

  @override
  State<EnhancedTimeline> createState() => _EnhancedTimelineState();
}

class _EnhancedTimelineState extends State<EnhancedTimeline>
    with TickerProviderStateMixin {
  final TemporaryVideoService _tempService = TemporaryVideoService.instance;

  // Animation controllers
  late AnimationController _trimAnimationController;
  late AnimationController _cutAnimationController;
  late Animation<double> _trimAnimation;
  late Animation<double> _cutAnimation;

  // Interaction state
  double _zoomLevel = 1.0;
  double _scrollOffset = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _trimAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _cutAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _trimAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _trimAnimationController,
      curve: Curves.easeInOut,
    ));

    _cutAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cutAnimationController,
      curve: Curves.elasticOut,
    ));

    // Start animations based on mode
    if (widget.isTrimMode) {
      _trimAnimationController.repeat(reverse: true);
    }
    if (widget.isCutMode) {
      _cutAnimationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(EnhancedTimeline oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle animation state changes
    if (widget.isTrimMode && !oldWidget.isTrimMode) {
      _trimAnimationController.repeat(reverse: true);
    } else if (!widget.isTrimMode && oldWidget.isTrimMode) {
      _trimAnimationController.stop();
      _trimAnimationController.reset();
    }

    if (widget.isCutMode && !oldWidget.isCutMode) {
      _cutAnimationController.repeat(reverse: true);
    } else if (!widget.isCutMode && oldWidget.isCutMode) {
      _cutAnimationController.stop();
      _cutAnimationController.reset();
    }
  }

  @override
  void dispose() {
    _trimAnimationController.dispose();
    _cutAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppConstants.timelineHeight,
      decoration: BoxDecoration(
        color: Color(AppConstants.surfaceColor),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1)),
        ),
      ),
      child: Column(
        children: [
          _buildTimelineHeader(),
          Expanded(
            child: _buildTimelineContent(),
          ),
          _buildTimelineFooter(),
        ],
      ),
    );
  }

  Widget _buildTimelineHeader() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Color(AppConstants.backgroundColor),
        border: Border(
          bottom: BorderSide(color: Colors.white.withValues(alpha: 0.1)),
        ),
      ),
      child: Row(
        children: [
          // Timeline controls
          _buildTimelineControls(),
          const Spacer(),
          // Zoom controls
          _buildZoomControls(),
        ],
      ),
    );
  }

  Widget _buildTimelineControls() {
    return Row(
      children: [
        // Snap to grid toggle
        IconButton(
          onPressed: () {
            // Toggle snap to grid
            HapticFeedback.lightImpact();
          },
          icon: const Icon(Icons.grid_on, color: Colors.white, size: 18),
          tooltip: 'Snap to Grid',
        ),

        // Preview temporary edits
        if (widget.onPreviewTemporaryEdits != null)
          IconButton(
            onPressed: widget.onPreviewTemporaryEdits,
            icon: const Icon(Icons.preview, color: Colors.blue, size: 18),
            tooltip: 'Preview Edits',
          ),

        // Reset temporary edits
        if (widget.onResetTemporaryEdits != null)
          IconButton(
            onPressed: widget.onResetTemporaryEdits,
            icon: const Icon(Icons.restore, color: Colors.orange, size: 18),
            tooltip: 'Reset Edits',
          ),
      ],
    );
  }

  Widget _buildZoomControls() {
    return Row(
      children: [
        IconButton(
          onPressed: _zoomOut,
          icon: const Icon(Icons.zoom_out, color: Colors.white, size: 18),
          tooltip: 'Zoom Out (Ctrl + -)',
        ),
        Container(
          width: 50,
          alignment: Alignment.center,
          child: Text(
            '${(_zoomLevel * 100).round()}%',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
        IconButton(
          onPressed: _zoomIn,
          icon: const Icon(Icons.zoom_in, color: Colors.white, size: 18),
          tooltip: 'Zoom In (Ctrl + +)',
        ),
      ],
    );
  }

  Widget _buildTimelineContent() {
    return Stack(
      children: [
        // Background grid
        _buildTimelineGrid(),

        // Media items with temporary edit visualization
        ..._buildMediaItemsWithTemporaryEdits(),

        // Trim/Cut indicators
        if (widget.isTrimMode || widget.isCutMode) _buildTrimCutIndicators(),

        // Playhead
        _buildPlayhead(),

        // Interaction overlay
        _buildInteractionOverlay(),
      ],
    );
  }

  Widget _buildTimelineGrid() {
    return CustomPaint(
      painter: EnhancedTimelineGridPainter(
        duration: widget.duration,
        zoomLevel: _zoomLevel,
        scrollOffset: _scrollOffset,
      ),
      size: Size.infinite,
    );
  }

  List<Widget> _buildMediaItemsWithTemporaryEdits() {
    final widgets = <Widget>[];

    for (final item in widget.mediaItems) {
      // Check if item has temporary edits
      final hasTemporaryEdits = _tempService.hasActiveTemporaryEdits(item.id);

      if (hasTemporaryEdits) {
        // Build segmented timeline for items with cuts
        widgets.addAll(_buildSegmentedMediaItem(item));
      } else {
        // Build normal media item
        widgets.add(_buildNormalMediaItem(item));
      }
    }

    return widgets;
  }

  List<Widget> _buildSegmentedMediaItem(MediaItem item) {
    final segments = _tempService.getTimelineSegments(item.id);
    final widgets = <Widget>[];

    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      widgets.add(_buildSegmentWidget(item, segment, i));

      // Add gap indicator between segments
      if (i < segments.length - 1) {
        widgets.add(_buildGapIndicator(
            segment.virtualEnd, segments[i + 1].virtualStart));
      }
    }

    return widgets;
  }

  Widget _buildSegmentWidget(
      MediaItem item, TimelineSegment segment, int index) {
    final trackIndex = _getTrackIndex(item.type);
    if (trackIndex == -1) return const SizedBox.shrink();

    return Positioned(
      left: _getPositionX(segment.virtualStart),
      top: _getTrackY(trackIndex),
      width: _getPositionX(segment.duration),
      height: AppConstants.trackHeight,
      child: AnimatedBuilder(
        animation: _trimAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              color: _getMediaItemColor(item.type).withValues(
                alpha: 0.8 + (_trimAnimation.value * 0.2),
              ),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Stack(
              children: [
                // Segment content
                _buildSegmentContent(item, segment, index),

                // Temporary edit indicator
                if (_tempService.hasActiveTemporaryEdits(item.id))
                  _buildTemporaryEditIndicator(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSegmentContent(
      MediaItem item, TimelineSegment segment, int index) {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${_getMediaTypeLabel(item.type)} ${index + 1}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            '${segment.originalStart.toStringAsFixed(1)}s - ${segment.originalEnd.toStringAsFixed(1)}s',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 8,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildTemporaryEditIndicator() {
    return Positioned(
      top: 2,
      right: 2,
      child: Container(
        width: 8,
        height: 8,
        decoration: const BoxDecoration(
          color: Colors.orange,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.edit,
          color: Colors.white,
          size: 6,
        ),
      ),
    );
  }

  // Helper methods
  double _getPositionX(double time) {
    return (time / widget.duration) * _getTimelineWidth() * _zoomLevel -
        _scrollOffset;
  }

  double _getTimelineWidth() {
    return MediaQuery.of(context).size.width - AppConstants.trackLabelWidth;
  }

  double _getTrackY(int trackIndex) {
    return trackIndex * (AppConstants.trackHeight + AppConstants.trackSpacing);
  }

  int _getTrackIndex(MediaType type) {
    switch (type) {
      case MediaType.video:
        return 0;
      case MediaType.audio:
        return 1;
      case MediaType.image:
        return 2;
      case MediaType.text:
        return 3;
    }
  }

  Color _getMediaItemColor(MediaType type) {
    switch (type) {
      case MediaType.video:
        return Colors.blue;
      case MediaType.audio:
        return Colors.green;
      case MediaType.image:
        return Colors.purple;
      case MediaType.text:
        return Colors.orange;
    }
  }

  String _getMediaTypeLabel(MediaType type) {
    switch (type) {
      case MediaType.video:
        return 'Video';
      case MediaType.audio:
        return 'Audio';
      case MediaType.image:
        return 'Image';
      case MediaType.text:
        return 'Text';
    }
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel = (_zoomLevel * 1.2).clamp(0.1, 5.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = (_zoomLevel / 1.2).clamp(0.1, 5.0);
    });
  }

  // Placeholder methods - to be implemented
  Widget _buildNormalMediaItem(MediaItem item) => const SizedBox.shrink();
  Widget _buildGapIndicator(double start, double end) =>
      const SizedBox.shrink();
  Widget _buildTrimCutIndicators() => const SizedBox.shrink();
  Widget _buildPlayhead() => const SizedBox.shrink();
  Widget _buildInteractionOverlay() => const SizedBox.shrink();
  Widget _buildTimelineFooter() => const SizedBox.shrink();
}

// Custom painter for enhanced timeline grid
class EnhancedTimelineGridPainter extends CustomPainter {
  final double duration;
  final double zoomLevel;
  final double scrollOffset;

  EnhancedTimelineGridPainter({
    required this.duration,
    required this.zoomLevel,
    required this.scrollOffset,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    // Draw vertical grid lines (time markers)
    final timeInterval = _calculateTimeInterval();
    final startTime = (scrollOffset / size.width) * duration / zoomLevel;
    final endTime = startTime + (duration / zoomLevel);

    for (double time = (startTime / timeInterval).floor() * timeInterval;
        time <= endTime;
        time += timeInterval) {
      final x = ((time / duration) * size.width * zoomLevel) - scrollOffset;
      if (x >= 0 && x <= size.width) {
        canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
      }
    }

    // Draw horizontal grid lines (track separators)
    for (int i = 1; i < 4; i++) {
      final y = i * (AppConstants.trackHeight + AppConstants.trackSpacing);
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  double _calculateTimeInterval() {
    // Calculate appropriate time interval based on zoom level
    final baseInterval = 1.0; // 1 second
    if (zoomLevel > 3.0) return baseInterval / 4; // 0.25 seconds
    if (zoomLevel > 2.0) return baseInterval / 2; // 0.5 seconds
    if (zoomLevel > 1.0) return baseInterval; // 1 second
    if (zoomLevel > 0.5) return baseInterval * 2; // 2 seconds
    return baseInterval * 5; // 5 seconds
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

import 'package:flutter/material.dart';
import '../models/video_project.dart';
import '../utils/constants.dart';

class VideoTimeline extends StatefulWidget {
  final double duration;
  final double currentPosition;
  final List<MediaItem> mediaItems;
  final Function(double) onPositionChanged;
  final Function(MediaItem) onMediaItemTap;
  final Function(MediaItem) onMediaItemDelete;
  final Function(MediaItem, double, double)?
      onMediaItemResize; // New callback for resize
  final Function(MediaItem, double)? onMediaItemMove; // New callback for move

  const VideoTimeline({
    super.key,
    required this.duration,
    required this.currentPosition,
    required this.mediaItems,
    required this.onPositionChanged,
    required this.onMediaItemTap,
    required this.onMediaItemDelete,
    this.onMediaItemResize,
    this.onMediaItemMove,
  });

  @override
  State<VideoTimeline> createState() => _VideoTimelineState();
}

class _VideoTimelineState extends State<VideoTimeline> {
  double _dragPosition = 0;
  bool _isDragging = false;

  // Drag and resize state
  MediaItem? _draggingItem;
  bool _isDraggingItem = false;
  bool _isResizingStart = false;
  bool _isResizingEnd = false;
  double _dragStartX = 0;
  double _originalStartTime = 0;
  double _originalEndTime = 0;

  @override
  Widget build(BuildContext context) {
    // Calculate dynamic height based on number of layers
    final maxLayer = widget.mediaItems.isEmpty
        ? 0
        : widget.mediaItems
            .map((item) => item.layer)
            .reduce((a, b) => a > b ? a : b);
    final dynamicHeight = AppConstants.timelineHeight +
        (maxLayer * 30.0); // 30px per additional layer

    return Container(
      height: dynamicHeight.clamp(
          AppConstants.timelineHeight, 300.0), // Max 300px height
      color: Color(AppConstants.surfaceColor),
      child: Column(
        children: [
          // Time ruler
          Container(
            height: 30,
            child: CustomPaint(
              painter: TimeRulerPainter(
                duration: widget.duration,
                currentPosition:
                    _isDragging ? _dragPosition : widget.currentPosition,
              ),
              size: Size.infinite,
            ),
          ),
          // Layer tracks
          Expanded(
            child: SingleChildScrollView(
              child: SizedBox(
                height: ((maxLayer + 1) * 40.0 + 20)
                    .clamp(100.0, 400.0), // Clamp height
                child: Stack(
                  children: [
                    // Background grid with layer separators
                    CustomPaint(
                      painter: LayerGridPainter(
                        duration: widget.duration,
                        layerCount: maxLayer + 1,
                      ),
                      size: Size.infinite,
                    ),
                    // Layer labels
                    _buildLayerLabels(maxLayer),
                    // Media items
                    ...widget.mediaItems
                        .map((item) => _buildMediaItemWidget(item)),
                    // Playhead
                    Positioned(
                      left: 60 +
                          _getPositionX(_isDragging
                              ? _dragPosition
                              : widget
                                  .currentPosition), // Offset for layer labels
                      top: 0,
                      bottom: 0,
                      child: Container(
                        width: 2,
                        color: Color(AppConstants.accentColor),
                        child: Transform.translate(
                          offset: const Offset(-3, 5),
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Color(AppConstants.accentColor),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaItemWidget(MediaItem item) {
    final left = _getPositionX(item.startTime);
    final width = _getPositionX(item.endTime) - left;

    Color itemColor;
    IconData itemIcon;

    switch (item.type) {
      case MediaType.video:
        itemColor = Colors.blue;
        itemIcon = Icons.videocam;
        break;
      case MediaType.audio:
        itemColor = Colors.green;
        itemIcon = Icons.audiotrack;
        break;
      case MediaType.image:
        itemColor = Colors.orange;
        itemIcon = Icons.image;
        break;
      case MediaType.text:
        itemColor = Colors.purple;
        itemIcon = Icons.text_fields;
        break;
    }

    return Positioned(
      left: left + 60, // Offset for layer labels
      top: _getLayerY(item.layer),
      width: width,
      height: 30, // Increased height for better visibility
      child: Stack(
        children: [
          // Main item container
          GestureDetector(
            onTap: () => widget.onMediaItemTap(item),
            onLongPress: () => _showMediaItemMenu(item),
            onPanStart: (details) => _onItemPanStart(item, details),
            onPanUpdate: (details) => _onItemPanUpdate(item, details),
            onPanEnd: (details) => _onItemPanEnd(item),
            child: Container(
              width: width,
              height: 20,
              decoration: BoxDecoration(
                color: itemColor.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: itemColor, width: 1),
              ),
              child: Row(
                children: [
                  // Layer indicator
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 4),
                    decoration: BoxDecoration(
                      color: itemColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${item.layer}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  Icon(itemIcon, size: 16, color: Colors.white),
                  if (width > 50)
                    Expanded(
                      child: Text(
                        _getItemDisplayText(item),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  // Delete button (visible when item is wide enough)
                  if (width > 80)
                    GestureDetector(
                      onTap: () => _showDeleteConfirmation(item),
                      child: Container(
                        margin: const EdgeInsets.only(left: 4, right: 4),
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: const Icon(
                          Icons.close,
                          size: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Left resize handle
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onPanStart: (details) => _onResizeStart(item, true, details),
              onPanUpdate: (details) => _onResizeUpdate(item, true, details),
              onPanEnd: (details) => _onResizeEnd(item),
              child: Container(
                width: 8,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.drag_indicator,
                    size: 8,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),

          // Right resize handle
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: GestureDetector(
              onPanStart: (details) => _onResizeStart(item, false, details),
              onPanUpdate: (details) => _onResizeUpdate(item, false, details),
              onPanEnd: (details) => _onResizeEnd(item),
              child: Container(
                width: 8,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.drag_indicator,
                    size: 8,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getPositionX(double time) {
    final screenWidth =
        MediaQuery.of(context).size.width - 60; // Subtract layer label width
    if (widget.duration <= 0 || screenWidth <= 0) return 0.0;
    return (time / widget.duration) * screenWidth;
  }

  // Removed _getTrackY method as it's replaced by _getLayerY

  double _getLayerY(int layer) {
    return 10 + (layer * 40.0); // 40px per layer with 10px top padding
  }

  Widget _buildLayerLabels(int maxLayer) {
    return Positioned(
      left: 0,
      top: 0,
      child: SizedBox(
        width: 60,
        child: Column(
          children: List.generate(maxLayer + 1, (index) {
            final layer = maxLayer - index; // Reverse order (top layer first)
            return Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Color(AppConstants.backgroundColor),
                border: Border(
                  bottom: BorderSide(color: Colors.white10, width: 0.5),
                  right: BorderSide(color: Colors.white10, width: 0.5),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'L$layer',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _getLayerTypeText(layer),
                    style: const TextStyle(
                      color: Colors.white54,
                      fontSize: 8,
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ),
    );
  }

  String _getLayerTypeText(int layer) {
    // Find the most common media type in this layer
    final layerItems =
        widget.mediaItems.where((item) => item.layer == layer).toList();
    if (layerItems.isEmpty) return '';

    // Determine layer type based on layer ranges
    if (layer < 100) {
      return 'Video';
    } else if (layer < 200) {
      return 'Audio';
    } else if (layer < 300) {
      return 'Image';
    } else {
      return 'Text';
    }
  }

  String _getItemDisplayText(MediaItem item) {
    switch (item.type) {
      case MediaType.text:
        return item.text ?? 'Text';
      case MediaType.video:
        return 'Video';
      case MediaType.audio:
        return 'Audio';
      case MediaType.image:
        return 'Image';
    }
  }

  void _showMediaItemMenu(MediaItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit'),
              onTap: () {
                Navigator.pop(context);
                widget.onMediaItemTap(item);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(MediaItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D2D2D),
        title: const Text(
          'Delete Media Item',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete this ${item.type.name} item?',
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getItemColor(item.type).withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: _getItemColor(item.type),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getItemIcon(item.type),
                    size: 16,
                    color: _getItemColor(item.type),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getItemDisplayText(item),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onMediaItemDelete(item);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getItemColor(MediaType type) {
    switch (type) {
      case MediaType.video:
        return Colors.blue;
      case MediaType.audio:
        return Colors.green;
      case MediaType.image:
        return Colors.orange;
      case MediaType.text:
        return Colors.purple;
    }
  }

  IconData _getItemIcon(MediaType type) {
    switch (type) {
      case MediaType.video:
        return Icons.videocam;
      case MediaType.audio:
        return Icons.audiotrack;
      case MediaType.image:
        return Icons.image;
      case MediaType.text:
        return Icons.text_fields;
    }
  }

  // Drag and resize methods
  void _onItemPanStart(MediaItem item, DragStartDetails details) {
    setState(() {
      _draggingItem = item;
      _isDraggingItem = true;
      _dragStartX = details.localPosition.dx;
      _originalStartTime = item.startTime;
      _originalEndTime = item.endTime;
    });
  }

  void _onItemPanUpdate(MediaItem item, DragUpdateDetails details) {
    if (!_isDraggingItem || _draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = _getTimeFromDeltaX(deltaX);

    final newStartTime =
        (_originalStartTime + deltaTime).clamp(0.0, widget.duration);

    if (widget.onMediaItemMove != null) {
      widget.onMediaItemMove!(item, newStartTime);
    }
  }

  void _onItemPanEnd(MediaItem item) {
    setState(() {
      _draggingItem = null;
      _isDraggingItem = false;
    });
  }

  void _onResizeStart(
      MediaItem item, bool isLeftHandle, DragStartDetails details) {
    setState(() {
      _draggingItem = item;
      _isResizingStart = isLeftHandle;
      _isResizingEnd = !isLeftHandle;
      _dragStartX = details.localPosition.dx;
      _originalStartTime = item.startTime;
      _originalEndTime = item.endTime;
    });
  }

  void _onResizeUpdate(
      MediaItem item, bool isLeftHandle, DragUpdateDetails details) {
    if (_draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = _getTimeFromDeltaX(deltaX);

    double newStartTime = _originalStartTime;
    double newEndTime = _originalEndTime;

    if (isLeftHandle) {
      // Resizing start time
      newStartTime =
          (_originalStartTime + deltaTime).clamp(0.0, _originalEndTime - 0.1);
    } else {
      // Resizing end time
      newEndTime = (_originalEndTime + deltaTime)
          .clamp(_originalStartTime + 0.1, widget.duration);
    }

    if (widget.onMediaItemResize != null) {
      widget.onMediaItemResize!(item, newStartTime, newEndTime);
    }
  }

  void _onResizeEnd(MediaItem item) {
    setState(() {
      _draggingItem = null;
      _isResizingStart = false;
      _isResizingEnd = false;
    });
  }

  double _getTimeFromDeltaX(double deltaX) {
    final screenWidth =
        MediaQuery.of(context).size.width - 60; // Subtract layer label width
    if (screenWidth <= 0 || widget.duration <= 0) return 0.0;
    return (deltaX / screenWidth) * widget.duration;
  }
}

class TimeRulerPainter extends CustomPainter {
  final double duration;
  final double currentPosition;

  TimeRulerPainter({
    required this.duration,
    required this.currentPosition,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Validate inputs to prevent NaN
    if (duration <= 0 || size.width <= 0 || size.height <= 0) return;

    final paint = Paint()
      ..color = Colors.white70
      ..strokeWidth = 1;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // Draw time markers
    final interval = duration / 10; // 10 markers
    for (int i = 0; i <= 10; i++) {
      final time = i * interval;
      final x = (time / duration) * size.width;

      // Validate x coordinate
      if (!x.isFinite) continue;

      // Draw tick
      canvas.drawLine(
        Offset(x, size.height - 10),
        Offset(x, size.height),
        paint,
      );

      // Draw time label
      final timeText = _formatTime(time);
      textPainter.text = TextSpan(
        text: timeText,
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 10,
        ),
      );
      textPainter.layout();

      final textX = x - textPainter.width / 2;
      if (textX.isFinite) {
        textPainter.paint(
          canvas,
          Offset(textX, 0),
        );
      }
    }
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final secs = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class GridPainter extends CustomPainter {
  final double duration;

  GridPainter({required this.duration});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white10
      ..strokeWidth = 0.5;

    // Draw vertical grid lines
    final interval = duration / 20; // 20 lines
    for (int i = 0; i <= 20; i++) {
      final time = i * interval;
      final x = (time / duration) * size.width;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal track separators
    for (int i = 1; i < 4; i++) {
      final y = i * 25.0;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class LayerGridPainter extends CustomPainter {
  final double duration;
  final int layerCount;

  LayerGridPainter({
    required this.duration,
    required this.layerCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Validate inputs to prevent NaN
    if (duration <= 0 || size.width <= 60 || size.height <= 0 || layerCount < 0)
      return;

    final paint = Paint()
      ..color = Colors.white10
      ..strokeWidth = 0.5;

    final boldPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2)
      ..strokeWidth = 1.0;

    // Draw vertical grid lines (time markers)
    final interval = duration / 20; // 20 lines
    for (int i = 0; i <= 20; i++) {
      final time = i * interval;
      final x = 60 +
          ((time / duration) * (size.width - 60)); // Offset for layer labels

      // Validate x coordinate
      if (!x.isFinite || x < 60 || x > size.width) continue;

      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        i % 5 == 0 ? boldPaint : paint, // Bold lines every 5th marker
      );
    }

    // Draw horizontal layer separators
    for (int i = 0; i <= layerCount; i++) {
      final y = 10 + (i * 40.0);

      // Validate y coordinate
      if (!y.isFinite || y < 0 || y > size.height) continue;

      canvas.drawLine(
        Offset(60, y), // Start after layer labels
        Offset(size.width, y),
        paint,
      );
    }

    // Draw layer label separator
    canvas.drawLine(
      Offset(60, 0),
      Offset(60, size.height),
      boldPaint,
    );

    // Draw layer backgrounds (alternating colors)
    for (int i = 0; i < layerCount; i++) {
      if (i % 2 == 0) {
        final y = 10 + (i * 40.0);
        final width = size.width - 60;

        // Validate rect parameters
        if (!y.isFinite || !width.isFinite || width <= 0 || y < 0) continue;

        final rect = Rect.fromLTWH(60, y, width, 40);

        final backgroundPaint = Paint()
          ..color = Colors.white.withValues(alpha: 0.02);

        canvas.drawRect(rect, backgroundPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

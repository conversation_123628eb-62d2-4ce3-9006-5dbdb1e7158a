import 'package:flutter/material.dart';
import '../models/video_project.dart';
import '../utils/constants.dart';

class MultiLayerWidget extends StatefulWidget {
  final List<MediaItem> mediaItems;
  final Function(List<MediaItem>) onLayersChanged;
  final Function(MediaItem) onItemSelected;
  final VoidCallback onCancel;

  const MultiLayerWidget({
    super.key,
    required this.mediaItems,
    required this.onLayersChanged,
    required this.onItemSelected,
    required this.onCancel,
  });

  @override
  State<MultiLayerWidget> createState() => _MultiLayerWidgetState();
}

class _MultiLayerWidgetState extends State<MultiLayerWidget> {
  late List<MediaItem> _mediaItems;
  MediaItem? _selectedItem;

  @override
  void initState() {
    super.initState();
    _mediaItems = List.from(widget.mediaItems);
    _sortItemsByLayer();
  }

  void _sortItemsByLayer() {
    _mediaItems.sort((a, b) => b.layer.compareTo(a.layer)); // Higher layer on top
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        padding: const EdgeInsets.all(20),
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Layer Management',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Layer info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Color(AppConstants.backgroundColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 16),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Higher layers appear on top. Drag to reorder layers.',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            
            // Layer list
            const Text(
              'Layers (Top to Bottom)',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            
            Expanded(
              child: ReorderableListView.builder(
                itemCount: _mediaItems.length,
                onReorder: _onReorder,
                itemBuilder: (context, index) {
                  final item = _mediaItems[index];
                  final isSelected = _selectedItem?.id == item.id;
                  
                  return Container(
                    key: ValueKey(item.id),
                    margin: const EdgeInsets.only(bottom: 8),
                    child: _buildLayerItem(item, index, isSelected),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Layer controls
            if (_selectedItem != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Color(AppConstants.backgroundColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Selected: ${_getItemDisplayName(_selectedItem!)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildLayerButton(
                          'Move Up',
                          Icons.keyboard_arrow_up,
                          () => _moveLayer(_selectedItem!, 1),
                        ),
                        _buildLayerButton(
                          'Move Down',
                          Icons.keyboard_arrow_down,
                          () => _moveLayer(_selectedItem!, -1),
                        ),
                        _buildLayerButton(
                          'To Top',
                          Icons.vertical_align_top,
                          () => _moveToTop(_selectedItem!),
                        ),
                        _buildLayerButton(
                          'To Bottom',
                          Icons.vertical_align_bottom,
                          () => _moveToBottom(_selectedItem!),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _applyChanges,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.accentColor),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLayerItem(MediaItem item, int index, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedItem = isSelected ? null : item;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected 
              ? Color(AppConstants.accentColor).withValues(alpha: 0.3)
              : Color(AppConstants.backgroundColor),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected 
                ? Color(AppConstants.accentColor)
                : Colors.white30,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Drag handle
            const Icon(
              Icons.drag_handle,
              color: Colors.white70,
              size: 20,
            ),
            const SizedBox(width: 12),
            
            // Layer number
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: _getMediaTypeColor(item.type),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Center(
                child: Text(
                  '${item.layer}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            
            // Media type icon
            Icon(
              _getMediaTypeIcon(item.type),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            
            // Item info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getItemDisplayName(item),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${item.startTime.toStringAsFixed(1)}s - ${item.endTime.toStringAsFixed(1)}s',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            
            // Visibility toggle
            IconButton(
              onPressed: () => _toggleVisibility(item),
              icon: Icon(
                item.opacity > 0 ? Icons.visibility : Icons.visibility_off,
                color: item.opacity > 0 ? Colors.white : Colors.white54,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLayerButton(String label, IconData icon, VoidCallback onPressed) {
    return Column(
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Color(AppConstants.accentColor),
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Color _getMediaTypeColor(MediaType type) {
    switch (type) {
      case MediaType.video:
        return Colors.blue;
      case MediaType.audio:
        return Colors.green;
      case MediaType.image:
        return Colors.orange;
      case MediaType.text:
        return Colors.purple;
    }
  }

  IconData _getMediaTypeIcon(MediaType type) {
    switch (type) {
      case MediaType.video:
        return Icons.videocam;
      case MediaType.audio:
        return Icons.audiotrack;
      case MediaType.image:
        return Icons.image;
      case MediaType.text:
        return Icons.text_fields;
    }
  }

  String _getItemDisplayName(MediaItem item) {
    switch (item.type) {
      case MediaType.text:
        return item.text ?? 'Text';
      case MediaType.video:
        return 'Video';
      case MediaType.audio:
        return 'Audio';
      case MediaType.image:
        return 'Image';
    }
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _mediaItems.removeAt(oldIndex);
      _mediaItems.insert(newIndex, item);
      _updateLayerNumbers();
    });
  }

  void _updateLayerNumbers() {
    for (int i = 0; i < _mediaItems.length; i++) {
      final newLayer = _mediaItems.length - i - 1; // Top item gets highest layer
      _mediaItems[i] = _mediaItems[i].copyWith(layer: newLayer);
    }
  }

  void _moveLayer(MediaItem item, int direction) {
    final currentIndex = _mediaItems.indexWhere((i) => i.id == item.id);
    if (currentIndex == -1) return;
    
    final newIndex = (currentIndex - direction).clamp(0, _mediaItems.length - 1);
    if (newIndex != currentIndex) {
      _onReorder(currentIndex, newIndex);
    }
  }

  void _moveToTop(MediaItem item) {
    final currentIndex = _mediaItems.indexWhere((i) => i.id == item.id);
    if (currentIndex > 0) {
      _onReorder(currentIndex, 0);
    }
  }

  void _moveToBottom(MediaItem item) {
    final currentIndex = _mediaItems.indexWhere((i) => i.id == item.id);
    if (currentIndex < _mediaItems.length - 1) {
      _onReorder(currentIndex, _mediaItems.length);
    }
  }

  void _toggleVisibility(MediaItem item) {
    setState(() {
      final index = _mediaItems.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _mediaItems[index] = _mediaItems[index].copyWith(
          opacity: _mediaItems[index].opacity > 0 ? 0.0 : 1.0,
        );
      }
    });
  }

  void _applyChanges() {
    widget.onLayersChanged(_mediaItems);
    widget.onCancel();
  }
}

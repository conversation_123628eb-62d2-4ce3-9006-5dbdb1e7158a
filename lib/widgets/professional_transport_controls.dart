import 'package:flutter/material.dart';
import '../utils/constants.dart';

class ProfessionalTransportControls extends StatelessWidget {
  final bool isPlaying;
  final double currentPosition;
  final double duration;
  final VoidCallback onPlayPause;
  final VoidCallback onStop;
  final VoidCallback? onRestart;
  final VoidCallback onSeekBackward;
  final VoidCallback onSeekForward;
  final VoidCallback onSkipToStart;
  final VoidCallback onSkipToEnd;
  final bool isTrimMode;
  final bool isCutMode;
  final VoidCallback onToggleTrimMode;
  final VoidCallback onToggleCutMode;
  final VoidCallback? onApplyTrimCut;
  final VoidCallback? onResetTrimCut;
  final String? currentVideoPath;

  const ProfessionalTransportControls({
    super.key,
    required this.isPlaying,
    required this.currentPosition,
    required this.duration,
    required this.onPlayPause,
    required this.onStop,
    this.onRestart,
    required this.onSeekBackward,
    required this.onSeekForward,
    required this.onSkipToStart,
    required this.onSkipToEnd,
    required this.isTrimMode,
    required this.isCutMode,
    required this.onToggleTrimMode,
    required this.onToggleCutMode,
    this.onApplyTrimCut,
    this.onResetTrimCut,
    this.currentVideoPath,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppConstants.toolbarHeight,
      decoration: BoxDecoration(
        color: Color(AppConstants.surfaceColor),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // Time display
              _buildTimeDisplay(),
              const SizedBox(width: 16),

              // Transport controls
              _buildTransportControls(),

              const SizedBox(width: 16),

              // Video status indicator
              _buildVideoStatus(),

              const SizedBox(width: 16),

              // Edit mode controls
              _buildEditControls(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Color(AppConstants.backgroundColor),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _formatTime(currentPosition),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFeatures: [FontFeature.tabularFigures()],
            ),
          ),
          Text(
            ' / ',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
          ),
          Text(
            _formatTime(duration),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
              fontWeight: FontWeight.w500,
              fontFeatures: const [FontFeature.tabularFigures()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransportControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Skip to start
        _buildControlButton(
          icon: Icons.skip_previous,
          onPressed: onSkipToStart,
          tooltip: 'Skip to Start',
        ),

        const SizedBox(width: 6),

        // Seek backward
        _buildControlButton(
          icon: Icons.replay_10,
          onPressed: onSeekBackward,
          tooltip: 'Seek Backward 10s',
        ),

        const SizedBox(width: 8),

        // Play/Pause (larger button)
        _buildPlayPauseButton(),

        const SizedBox(width: 8),

        // Seek forward
        _buildControlButton(
          icon: Icons.forward_10,
          onPressed: onSeekForward,
          tooltip: 'Seek Forward 10s',
        ),

        const SizedBox(width: 6),

        // Skip to end
        _buildControlButton(
          icon: Icons.skip_next,
          onPressed: onSkipToEnd,
          tooltip: 'Skip to End',
        ),

        const SizedBox(width: 8),

        // Stop
        _buildControlButton(
          icon: Icons.stop,
          onPressed: onStop,
          tooltip: 'Stop',
        ),

        // Restart (if available)
        if (onRestart != null) ...[
          const SizedBox(width: 6),
          _buildControlButton(
            icon: Icons.restart_alt,
            onPressed: onRestart!,
            tooltip: 'Restart from Beginning',
          ),
        ],
      ],
    );
  }

  Widget _buildVideoStatus() {
    final hasVideo = currentVideoPath != null;
    final videoName = hasVideo ? currentVideoPath!.split('/').last : 'No video';

    return Container(
      constraints: const BoxConstraints(maxWidth: 150), // Limit width
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: hasVideo
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: hasVideo ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            hasVideo ? Icons.videocam : Icons.videocam_off,
            color: hasVideo ? Colors.green : Colors.red,
            size: 12,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              videoName,
              style: TextStyle(
                color: hasVideo ? Colors.green : Colors.red,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Trim mode
        _buildModeButton(
          icon: Icons.content_cut,
          isActive: isTrimMode,
          onPressed: onToggleTrimMode,
          tooltip: 'Trim Mode - Select start and end points',
          label: 'Trim',
        ),

        const SizedBox(width: 12),

        // Simple Cut button - one click to cut at current position
        _buildSimpleCutButton(),

        // Apply and Reset buttons (only show when in trim mode)
        if (isTrimMode) ...[
          const SizedBox(width: 16),

          // Apply button
          if (onApplyTrimCut != null)
            _buildActionButton(
              icon: Icons.check,
              onPressed: onApplyTrimCut!,
              tooltip: 'Apply Trim',
              color: Colors.green,
            ),

          const SizedBox(width: 8),

          // Reset button
          if (onResetTrimCut != null)
            _buildActionButton(
              icon: Icons.close,
              onPressed: onResetTrimCut!,
              tooltip: 'Cancel Trim',
              color: Colors.red,
            ),
        ],
      ],
    );
  }

  Widget _buildSimpleCutButton() {
    return Tooltip(
      message: 'Cut at current position - Click to split video here',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onToggleCutMode,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.red,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.call_split,
                  color: Colors.red,
                  size: 16,
                ),
                const SizedBox(width: 6),
                const Text(
                  'Cut Here',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 18,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayPauseButton() {
    return Tooltip(
      message: isPlaying ? 'Pause' : 'Play',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPlayPause,
          borderRadius: BorderRadius.circular(10),
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Color(AppConstants.accentColor),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Color(AppConstants.accentColor).withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModeButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onPressed,
    required String tooltip,
    required String label,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isActive
                  ? Color(AppConstants.accentColor).withValues(alpha: 0.2)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isActive
                    ? Color(AppConstants.accentColor)
                    : Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color:
                      isActive ? Color(AppConstants.accentColor) : Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  label,
                  style: TextStyle(
                    color: isActive
                        ? Color(AppConstants.accentColor)
                        : Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required Color color,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: color, width: 1),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(double seconds) {
    final duration = Duration(seconds: seconds.round());
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    }
  }
}

import 'package:flutter/material.dart';
import '../models/video_project.dart';
import '../utils/constants.dart';

class TextOverlayWidget extends StatefulWidget {
  final MediaItem? textItem;
  final Function(MediaItem) onTextUpdated;
  final VoidCallback onCancel;

  const TextOverlayWidget({
    super.key,
    this.textItem,
    required this.onTextUpdated,
    required this.onCancel,
  });

  @override
  State<TextOverlayWidget> createState() => _TextOverlayWidgetState();
}

class _TextOverlayWidgetState extends State<TextOverlayWidget> {
  late TextEditingController _textController;
  late double _fontSize;
  late Color _textColor;
  late double _startTime;
  late double _endTime;
  late double _x;
  late double _y;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    if (widget.textItem != null) {
      _textController =
          TextEditingController(text: widget.textItem!.text ?? '');
      _fontSize = widget.textItem!.fontSize ?? AppConstants.defaultTextSize;
      _textColor = Color(widget.textItem!.textColor ?? 0xFFFFFFFF);
      _startTime = widget.textItem!.startTime;
      _endTime = widget.textItem!.endTime;
      _x = widget.textItem!.x ?? 50;
      _y = widget.textItem!.y ?? 50;

      // Validate and fix time values
      _validateTimeValues();
    } else {
      _textController = TextEditingController();
      _fontSize = AppConstants.defaultTextSize;
      _textColor = Colors.white;
      _startTime = 0;
      _endTime = 5;
      _x = 50;
      _y = 50;

      // Ensure end time is always greater than start time
      _validateTimeValues();
    }
  }

  /// Validate and fix time values to prevent slider errors
  void _validateTimeValues() {
    // Ensure start time is within bounds
    _startTime = _startTime.clamp(0.0, 59.0);

    // Ensure end time is at least 1 second after start time
    final minEndTime = _startTime + 1.0;
    _endTime = _endTime.clamp(minEndTime, 60.0);

    // If end time would exceed max, adjust start time
    if (_endTime >= 60.0 && _startTime >= 59.0) {
      _startTime = 58.0;
      _endTime = 60.0;
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        padding: const EdgeInsets.all(20),
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85, // Fixed height
        child: SingleChildScrollView(
          // Make it scrollable
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Add Text Overlay',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: widget.onCancel,
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Text input
              TextField(
                controller: _textController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Text',
                  labelStyle: const TextStyle(color: Colors.white70),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(color: Color(AppConstants.accentColor)),
                  ),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 20),

              // Font size slider
              Text(
                'Font Size: ${_fontSize.round()}',
                style: const TextStyle(color: Colors.white),
              ),
              Slider(
                value: _fontSize,
                min: AppConstants.minTextSize,
                max: AppConstants.maxTextSize,
                divisions: 60,
                activeColor: Color(AppConstants.accentColor),
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                  });
                },
              ),
              const SizedBox(height: 20),

              // Color picker
              const Text(
                'Text Color',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 10),
              Wrap(
                spacing: 10,
                children: [
                  Colors.white,
                  Colors.black,
                  Colors.red,
                  Colors.blue,
                  Colors.green,
                  Colors.yellow,
                  Colors.purple,
                  Colors.orange,
                ]
                    .map((color) => GestureDetector(
                          onTap: () {
                            setState(() {
                              _textColor = color;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: _textColor == color
                                    ? Color(AppConstants.accentColor)
                                    : Colors.transparent,
                                width: 3,
                              ),
                            ),
                          ),
                        ))
                    .toList(),
              ),
              const SizedBox(height: 20),

              // Position sliders
              Text(
                'Position X: ${_x.round()}',
                style: const TextStyle(color: Colors.white),
              ),
              Slider(
                value: _x,
                min: 0,
                max: 300,
                activeColor: Color(AppConstants.accentColor),
                onChanged: (value) {
                  setState(() {
                    _x = value;
                  });
                },
              ),

              Text(
                'Position Y: ${_y.round()}',
                style: const TextStyle(color: Colors.white),
              ),
              Slider(
                value: _y,
                min: 0,
                max: 300,
                activeColor: Color(AppConstants.accentColor),
                onChanged: (value) {
                  setState(() {
                    _y = value;
                  });
                },
              ),
              const SizedBox(height: 20),

              // Time range
              Text(
                'Start Time: ${_startTime.toStringAsFixed(1)}s',
                style: const TextStyle(color: Colors.white),
              ),
              Slider(
                value: _startTime.clamp(0.0, 59.0),
                min: 0,
                max: 59, // Max 59 to ensure end time can be at least 60
                activeColor: Color(AppConstants.accentColor),
                onChanged: (value) {
                  setState(() {
                    _startTime = value;
                    // Ensure end time is always at least 1 second after start time
                    final minEndTime = _startTime + 1.0;
                    if (_endTime < minEndTime) {
                      _endTime = minEndTime.clamp(minEndTime, 60.0);
                    }
                  });
                },
              ),

              Text(
                'End Time: ${_endTime.toStringAsFixed(1)}s',
                style: const TextStyle(color: Colors.white),
              ),
              Slider(
                value: _endTime.clamp(_startTime + 1.0, 60.0),
                min: (_startTime + 1.0).clamp(1.0, 60.0),
                max: 60,
                activeColor: Color(AppConstants.accentColor),
                onChanged: (value) {
                  setState(() {
                    _endTime = value.clamp(_startTime + 1.0, 60.0);
                  });
                },
              ),
              const SizedBox(height: 20),

              // Preview
              Container(
                width: double.infinity,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      left: _x / 300 * MediaQuery.of(context).size.width * 0.8,
                      top: _y / 300 * 40,
                      child: Text(
                        _textController.text.isEmpty
                            ? 'Preview Text'
                            : _textController.text,
                        style: TextStyle(
                          color: _textColor,
                          fontSize: _fontSize / 3, // Scaled down for preview
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: widget.onCancel,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                    ),
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton(
                    onPressed: _saveText,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(AppConstants.accentColor),
                    ),
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveText() {
    if (_textController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter some text')),
      );
      return;
    }

    final textItem = MediaItem(
      id: widget.textItem?.id ??
          DateTime.now().millisecondsSinceEpoch.toString(),
      type: MediaType.text,
      text: _textController.text.trim(),
      startTime: _startTime,
      endTime: _endTime,
      x: _x,
      y: _y,
      fontSize: _fontSize,
      textColor: _textColor.value, // Convert Color to int
      layer: widget.textItem?.layer ?? _getNextTextLayer(),
    );

    widget.onTextUpdated(textItem);
  }

  int _getNextTextLayer() {
    // For new text items, assign to text layer (layer 3 or higher)
    // This is a simplified approach - in a real app, you'd get this from the parent
    return 3;
  }
}

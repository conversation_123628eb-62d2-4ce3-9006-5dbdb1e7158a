import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../models/video_project.dart';
import '../services/video_service.dart';
import '../widgets/professional_timeline.dart';
import '../widgets/professional_transport_controls.dart';
import '../widgets/text_overlay_widget.dart';
import '../widgets/filter_widget.dart';
import '../widgets/transform_widget.dart';
import '../widgets/multi_layer_widget.dart';
import '../widgets/crop_widget.dart';
import '../widgets/resizable_widget.dart';

import '../utils/constants.dart';
import '../utils/vendor_error_handler.dart';
import '../utils/undo_manager.dart' as custom_undo;
import '../services/temporary_video_service.dart';
import '../widgets/enhanced_transport_controls.dart';

class EditorScreen extends StatefulWidget {
  final String? videoPath;
  final VideoProject? project;

  const EditorScreen({
    super.key,
    this.videoPath,
    this.project,
  });

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<EditorScreen> {
  late VideoPlayerController _videoController;
  late VideoProject _currentProject;
  final VideoService _videoService = VideoService.instance;
  final TemporaryVideoService _tempVideoService =
      TemporaryVideoService.instance;
  final ImagePicker _imagePicker = ImagePicker();

  // Audio players for background music
  final Map<String, AudioPlayer> _audioPlayers = {};
  final List<String> _activeAudioIds = [];

  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isExporting = false;
  double _currentPosition = 0;
  double _videoDuration = 0;

  // Enhanced Trim/Cut state with temporary editing
  double? _trimStart;
  double? _trimEnd;
  bool _isTrimMode = false;
  bool _isCutMode = false;
  bool _isPreviewMode = false;
  bool _useEnhancedTimeline = true; // Toggle between old and new timeline

  // Selected item for resizing
  String? _selectedItemId;
  final Set<String> _selectedItemIds = {}; // For bulk selection

  // Undo/Redo manager
  final custom_undo.UndoManager _undoManager = custom_undo.UndoManager();

  // Video switching control
  bool _isSwitchingVideo = false;
  int _lastVideoSwitchCheck = 0;
  String? _currentVideoPath;

  @override
  void initState() {
    super.initState();
    _currentVideoPath = widget.videoPath;
    if (widget.videoPath != null) {
      _initializeVideo();
    } else {
      _initializeEmptyProject();
    }
    _initializeProject();
  }

  Future<void> _initializeVideo() async {
    if (widget.videoPath == null) return;

    try {
      _videoController = VideoPlayerController.file(File(widget.videoPath!));
      await _videoController.initialize();

      setState(() {
        _videoDuration =
            _videoController.value.duration.inMilliseconds / 1000.0;
        _isLoading = false;
      });

      _videoController.addListener(_videoListener);
    } catch (e) {
      // Use VendorErrorHandler to handle vendor-specific errors
      VendorErrorHandler.handleVendorError(
        e,
        fallbackValue: null,
        context: 'video initialization',
        onVendorError: () {
          // Try to continue with initialization despite vendor error
          try {
            setState(() {
              _videoDuration = _videoController.value.isInitialized
                  ? _videoController.value.duration.inMilliseconds / 1000.0
                  : 30.0; // Default duration if can't get actual duration
              _isLoading = false;
            });

            if (_videoController.value.isInitialized) {
              _videoController.addListener(_videoListener);
            }
          } catch (fallbackError) {
            // If all fails, set default values
            setState(() {
              _videoDuration = 30.0;
              _isLoading = false;
            });
          }
        },
      );
    }
  }

  void _initializeEmptyProject() {
    setState(() {
      _videoDuration = 60.0; // Default 60 seconds for empty project
      _isLoading = false;
    });

    // Create a dummy video controller for empty project
    // This will be replaced when first video is added
    _createDummyVideoController();
  }

  void _createDummyVideoController() {
    // For empty project, we'll initialize a minimal controller state
    // The UI will handle the null video case by showing empty canvas
    try {
      // Initialize with a dummy controller that won't be used
      // This prevents null reference errors in video control methods
      _isPlaying = false;
      _currentPosition = 0.0;
    } catch (e) {
      print('Error creating dummy controller: $e');
    }
  }

  Future<void> _initializeFirstVideo(String videoPath) async {
    try {
      print('🎬 INITIALIZING FIRST VIDEO: $videoPath');

      // Dispose any existing controller if present
      if (widget.videoPath != null) {
        print('   Disposing existing controller...');
        _videoController.removeListener(_videoListener);
        await _videoController.dispose();
      }

      // Initialize new video controller
      print('   Creating new video controller...');
      _videoController = VideoPlayerController.file(File(videoPath));

      print('   Initializing video controller...');
      await _videoController.initialize();

      // Update current video path
      _currentVideoPath = videoPath;

      // Set video duration
      _videoDuration = _videoController.value.duration.inMilliseconds / 1000.0;

      // Add listener AFTER initialization
      print('   Adding video listener...');
      _videoController.addListener(_videoListener);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // Test video listener by checking if it's working
      print('✅ Video initialized successfully!');
      print('   - Initialized: ${_videoController.value.isInitialized}');
      print('   - Duration: $_videoDuration seconds');
      print('   - Listener added: true');
      print('   - Current position: $_currentPosition');

      // Test if listener is working by manually triggering it
      Future.delayed(const Duration(milliseconds: 500), () {
        print('🔍 Testing video listener after 500ms...');
        if (_videoController.value.isInitialized) {
          final pos = _videoController.value.position.inMilliseconds / 1000.0;
          print(
              '   Video position from controller: ${pos.toStringAsFixed(1)}s');
        }
      });
    } catch (e) {
      print('❌ Error initializing first video: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _initializeProject() {
    if (widget.project != null) {
      _currentProject = widget.project!;
    } else if (widget.videoPath != null) {
      // Create main video as MediaItem
      final mainVideoItem = MediaItem(
        id: 'main_video_${DateTime.now().millisecondsSinceEpoch}',
        type: MediaType.video,
        filePath: widget.videoPath,
        startTime: 0.0,
        endTime: _videoDuration,
        layer: 0, // Main video at layer 0
        // No x, y, width, height for main video - it fills the entire preview
      );

      // Initialize project with main video as first MediaItem
      _currentProject = VideoProject(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: 'New Project',
        mediaItems: [mainVideoItem],
        duration: _videoDuration,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } else {
      // Initialize empty project
      _currentProject = VideoProject(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: 'Empty Project',
        mediaItems: [],
        duration: _videoDuration,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  // Throttle setState calls to prevent excessive rebuilds
  int _lastSetStateTime = 0;
  int _lastAudioSyncTime = 0;
  int _lastDebugTime = 0;
  static const int _setStateThrottleMs = 33; // ~30 FPS

  // Timer untuk memaksa video update
  Timer? _videoUpdateTimer;

  // Video end detection
  bool _hasVideoEnded = false;

  // Progress tracking
  double get _progressPercentage {
    if (_videoDuration <= 0) return 0.0;
    return (_currentPosition / _videoDuration).clamp(0.0, 1.0);
  }

  void _videoListener() {
    // Check all conditions and log them
    final isMounted = mounted;
    final isSwitching = _isSwitchingVideo;
    final isInitialized = _videoController.value.isInitialized;
    final isPlaying = _videoController.value.isPlaying;

    if (!isMounted || isSwitching || !isInitialized) {
      print(
          'Video listener blocked: mounted=$isMounted, switching=$isSwitching, initialized=$isInitialized');
      return;
    }

    final newPosition = _videoController.value.position.inMilliseconds / 1000.0;
    final now = DateTime.now().millisecondsSinceEpoch;

    // Always update position for timeline indicator
    final oldPosition = _currentPosition;
    _currentPosition = newPosition;

    // Debug: Print position every 200ms to verify listener is working
    if (now - _lastDebugTime > 200) {
      _lastDebugTime = now;
      print(
          '🎬 Video listener ACTIVE: pos=${newPosition.toStringAsFixed(2)}s, playing=$_isPlaying, controller_playing=$isPlaying');
      print(
          '   Position changed: ${oldPosition.toStringAsFixed(2)}s -> ${newPosition.toStringAsFixed(2)}s');
      print(
          '   Video duration: ${_videoController.value.duration.inMilliseconds / 1000.0}s');
    }

    // ALWAYS call setState to update timeline - no throttling for debugging
    if (mounted) {
      setState(() {
        // Force timeline update with current position
      });
      if (now - _lastDebugTime <= 200) {
        print('   setState called for timeline update');
      }
    }

    // Check if video has ended
    final videoDuration =
        _videoController.value.duration.inMilliseconds / 1000.0;
    if (newPosition >= videoDuration - 0.1 && _isPlaying) {
      print(
          '🎬 Video ended at ${newPosition.toStringAsFixed(2)}s, stopping all audio');
      _hasVideoEnded = true;

      // Update state immediately to prevent race conditions
      setState(() {
        _isPlaying = false;
        _currentPosition = videoDuration; // Set to exact end
      });

      _pauseAllAudio();
      _stopVideoUpdateTimer();
      return;
    }

    // Reset video ended flag if we're not at the end
    if (newPosition < videoDuration - 0.1) {
      _hasVideoEnded = false;
    }

    // Sync state between UI and video controller
    // Only force play if there's a clear mismatch and we're not at the end
    if (_isPlaying &&
        !isPlaying &&
        !_hasVideoEnded &&
        newPosition < videoDuration - 0.1) {
      print(
          '⚠️ Video should be playing but controller says not playing - forcing play');

      // Use Future.microtask to avoid blocking the listener
      Future.microtask(() async {
        try {
          if (_isPlaying && !_hasVideoEnded && mounted) {
            await _videoController.play();
          }
        } catch (e) {
          print('Error forcing video play: $e');
        }
      });
    }

    // Sync audio playback with video position (less frequently and only if video is stable)
    if (_isPlaying &&
        _videoController.value.isPlaying &&
        now - _lastAudioSyncTime > 500) {
      _lastAudioSyncTime = now;
      _syncAudioPlayback();
    }

    // Check for video transitions and continuous playback (less frequently)
    if (now - _lastVideoSwitchCheck > 1000) {
      _lastVideoSwitchCheck = now;

      // Check if current video has ended and we need to continue to next video
      _checkContinuousPlayback(newPosition);

      // Update video for current position
      _updateVideoForCurrentPosition();
    }
  }

  void _updateVideoForCurrentPosition() {
    // Skip if already switching or no videos in project
    if (_isSwitchingVideo || _currentProject.mediaItems.isEmpty) return;

    // Find the video that should be playing at current position
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .where((item) =>
            _currentPosition >= item.startTime &&
            _currentPosition <= item.endTime)
        .toList();

    if (videoItems.isNotEmpty) {
      // Sort by layer to get the topmost video
      videoItems.sort((a, b) => b.layer.compareTo(a.layer));
      final activeVideo = videoItems.first;

      // Only switch if it's a different video and we're not already switching
      if (activeVideo.filePath != _currentVideoPath &&
          !_isSwitchingVideo &&
          _videoController.value.isInitialized) {
        // Add a small delay to prevent rapid switching
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!_isSwitchingVideo && activeVideo.filePath != _currentVideoPath) {
            _switchVideoController(activeVideo.filePath!, activeVideo);
          }
        });
      }
    }
  }

  /// Check if current video has ended and continue to next video
  void _checkContinuousPlayback(double currentPosition) {
    if (!_isPlaying || _isSwitchingVideo) return;

    // Get all video items sorted by start time
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));

    if (videoItems.isEmpty) return;

    // Find current active video
    final currentVideo = videoItems.firstWhere(
      (item) => item.filePath == _currentVideoPath,
      orElse: () => videoItems.first,
    );

    // Check if we've reached the end of current video
    final relativePosition = currentPosition - currentVideo.startTime;
    final videoDuration = _videoController.value.isInitialized
        ? _videoController.value.duration.inMilliseconds / 1000.0
        : 0.0;

    // If we're near the end of current video (within 0.1 seconds)
    if (relativePosition >= videoDuration - 0.1 && videoDuration > 0) {
      // Find next video that should start
      final nextVideo = videoItems.firstWhere(
        (item) =>
            item.startTime > currentVideo.endTime - 0.1 &&
            item.startTime <= currentPosition + 0.5, // Look ahead 0.5 seconds
        orElse: () => videoItems.firstWhere(
          (item) => item.startTime >= currentVideo.endTime,
          orElse: () => currentVideo,
        ),
      );

      if (nextVideo.id != currentVideo.id && nextVideo.filePath != null) {
        debugPrint(
            'Continuous playback: transitioning from ${currentVideo.id} to ${nextVideo.id}');

        // Calculate the position in the timeline where next video should start
        final nextVideoPosition = nextVideo.startTime;

        // Switch to next video
        _switchVideoController(nextVideo.filePath!, nextVideo).then((_) {
          // Update timeline position to match next video start
          setState(() {
            _currentPosition = nextVideoPosition;
          });
        });
      }
    }
  }

  /// Handle video transitions triggered by timeline
  void _handleVideoTransition(double position) {
    debugPrint('Timeline triggered video transition at position: ${position}s');

    // Update current position to the transition position
    setState(() {
      _currentPosition = position;
    });

    // Force video update for the new position
    _updateVideoForCurrentPosition();
  }

  Future<void> _switchVideoController(
      String videoPath, MediaItem videoItem) async {
    if (_currentVideoPath == videoPath || _isSwitchingVideo) return;

    try {
      _isSwitchingVideo = true;
      print('🔄 Switching video controller to: $videoPath');

      // Store current playing state
      final wasPlaying = _isPlaying;

      // Update state immediately to prevent race conditions
      if (mounted) {
        setState(() {
          _isPlaying = false; // Temporarily pause during switch
        });
      }

      // Pause current video
      if (_videoController.value.isInitialized) {
        await _videoController.pause();
      }

      // Dispose current controller safely
      _videoController.removeListener(_videoListener);
      await _videoController.dispose();

      // Create new controller
      _videoController = VideoPlayerController.file(File(videoPath));
      await _videoController.initialize();

      // Calculate relative position within the video
      final relativePosition = _currentPosition - videoItem.startTime;
      final videoDuration =
          _videoController.value.duration.inMilliseconds / 1000.0;

      if (relativePosition >= 0 && relativePosition <= videoDuration) {
        await _videoController
            .seekTo(Duration(milliseconds: (relativePosition * 1000).round()));
      }

      // Set video volume based on audio presence
      final videoVolume = _getVideoVolume(videoItem);
      await _videoController.setVolume(videoVolume);

      // Add listener before resuming playback
      _videoController.addListener(_videoListener);
      _currentVideoPath = videoPath;

      // Resume playing if it was playing before
      if (wasPlaying && mounted) {
        await _videoController.play();

        // Update state after successful play
        setState(() {
          _isPlaying = true;
        });
      } else if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }

      print('✅ Video controller switched successfully');
    } catch (e) {
      print('❌ Error switching video controller: $e');

      // Reset state on error
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    } finally {
      _isSwitchingVideo = false;
    }
  }

  @override
  void dispose() async {
    try {
      // Set flag to prevent any more video switching
      _isSwitchingVideo = true;

      // Stop video update timer
      _stopVideoUpdateTimer();

      // Dispose all audio players
      for (final player in _audioPlayers.values) {
        await player.dispose();
      }
      _audioPlayers.clear();
      _activeAudioIds.clear();

      // Remove listener first
      if (_currentVideoPath != null && _videoController.value.isInitialized) {
        _videoController.removeListener(_videoListener);
        await _videoController.dispose();
      }
    } catch (e) {
      // Ignore dispose errors
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      autofocus: true,
      onKeyEvent: (node, event) {
        // Handle Delete key for single or bulk delete
        if (event.logicalKey.keyLabel == 'Delete') {
          if (_selectedItemIds.isNotEmpty) {
            // Bulk delete
            _deleteMultipleItems();
          } else if (_selectedItemId != null) {
            // Single delete
            final selectedItem = _getSelectedMediaItem();
            if (selectedItem != null) {
              _deleteMediaItemWithUndo(selectedItem);
            }
          }
          return KeyEventResult.handled;
        }

        // Handle Ctrl+Z for undo
        if (event.logicalKey.keyLabel == 'Z' &&
            HardwareKeyboard.instance.isControlPressed) {
          _undo();
          return KeyEventResult.handled;
        }

        // Handle Ctrl+Y for redo
        if (event.logicalKey.keyLabel == 'Y' &&
            HardwareKeyboard.instance.isControlPressed) {
          _redo();
          return KeyEventResult.handled;
        }

        return KeyEventResult.ignored;
      },
      child: Scaffold(
        backgroundColor: Color(AppConstants.backgroundColor),
        appBar: AppBar(
          title: Text(
            _currentProject.name,
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Color(AppConstants.surfaceColor),
          iconTheme: const IconThemeData(color: Colors.white),
          actions: [
            // Timeline toggle button
            IconButton(
              onPressed: () {
                setState(() {
                  _useEnhancedTimeline = !_useEnhancedTimeline;
                });
                _showSuccessSnackBar(_useEnhancedTimeline
                    ? 'Enhanced timeline activated with temporary editing'
                    : 'Classic timeline activated');
              },
              icon: Icon(
                  _useEnhancedTimeline ? Icons.timeline : Icons.view_timeline),
              tooltip: _useEnhancedTimeline
                  ? 'Switch to Classic Timeline'
                  : 'Switch to Enhanced Timeline',
            ),
            // Undo button
            IconButton(
              onPressed: _undoManager.canUndo ? _undo : null,
              icon: const Icon(Icons.undo),
              tooltip: _undoManager.canUndo
                  ? 'Undo: ${_undoManager.undoDescription}'
                  : 'Nothing to undo',
            ),
            // Redo button
            IconButton(
              onPressed: _undoManager.canRedo ? _redo : null,
              icon: const Icon(Icons.redo),
              tooltip: _undoManager.canRedo
                  ? 'Redo: ${_undoManager.redoDescription}'
                  : 'Nothing to redo',
            ),
            if (_isExporting)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              IconButton(
                onPressed: _exportVideo,
                icon: const Icon(Icons.download),
                tooltip: 'Export Video',
              ),
            IconButton(
              onPressed: _saveProject,
              icon: const Icon(Icons.save),
              tooltip: 'Save Project',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Column(
                children: [
                  // Video preview
                  Expanded(
                    flex: 3,
                    child: Container(
                      color: Colors.black,
                      child: Center(
                        child: AspectRatio(
                          aspectRatio: _currentVideoPath != null &&
                                  _videoController.value.isInitialized
                              ? _videoController.value.aspectRatio
                              : 16 /
                                  9, // Default aspect ratio for empty project
                          child: GestureDetector(
                            onTap: () {
                              // Deselect item when tapping on empty area
                              setState(() {
                                _selectedItemId = null;
                              });
                            },
                            child: Stack(
                              key: ValueKey(
                                  'video_stack_${_currentVideoPath ?? 'empty'}'),
                              children: [
                                // Main video player or empty canvas
                                if (_currentVideoPath != null &&
                                    _videoController.value.isInitialized)
                                  VideoPlayer(_videoController)
                                else
                                  _buildEmptyCanvas(),
                                // Overlay media items (all items including video overlays)
                                ..._buildOverlayItems(),
                                // Trim indicators
                                if (_isTrimMode) _buildTrimIndicators(),
                                // Cut indicators
                                if (_isCutMode) _buildCutIndicators(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Enhanced Transport Controls with temporary editing
                  _useEnhancedTimeline
                      ? EnhancedTransportControls(
                          isPlaying: _isPlaying,
                          currentPosition: _currentPosition,
                          duration: _videoDuration,
                          onPlayPause: _togglePlayPause,
                          onStop: _stopVideo,
                          onRestart: _restartVideo,
                          onSeekBackward: _seekBackward,
                          onSeekForward: _seekForward,
                          onSkipToStart: _skipToStart,
                          onSkipToEnd: _skipToEnd,
                          isTrimMode: _isTrimMode,
                          isCutMode: _isCutMode,
                          isPreviewMode: _isPreviewMode,
                          onToggleTrimMode: _toggleTrimMode,
                          onToggleCutMode: _toggleCutMode,
                          onApplyTemporaryTrim: _applyTemporaryTrim,
                          onApplyTemporaryCut: _applyTemporaryCut,
                          onResetTemporaryEdits: _resetTrimCut,
                          onPreviewTemporaryEdits: _previewTemporaryEdits,
                          currentVideoPath: _getActiveVideoPath(),
                        )
                      : ProfessionalTransportControls(
                          isPlaying: _isPlaying,
                          currentPosition: _currentPosition,
                          duration: _videoDuration,
                          onPlayPause: _togglePlayPause,
                          onStop: _stopVideo,
                          onRestart: _restartVideo,
                          onSeekBackward: _seekBackward,
                          onSeekForward: _seekForward,
                          onSkipToStart: _skipToStart,
                          onSkipToEnd: _skipToEnd,
                          isTrimMode: _isTrimMode,
                          isCutMode: _isCutMode,
                          onToggleTrimMode: _toggleTrimMode,
                          onToggleCutMode: _toggleCutMode,
                          onApplyTrimCut: _applyTrimCut,
                          onResetTrimCut: _resetTrimCut,
                          currentVideoPath: _getActiveVideoPath(),
                        ),

                  // Professional Timeline
                  SizedBox(
                    height: AppConstants.timelineHeight,
                    child: ProfessionalTimeline(
                      duration: _videoDuration,
                      currentPosition: _currentPosition,
                      mediaItems: _currentProject.mediaItems,
                      onPositionChanged: _seekToPosition,
                      onVideoTransition: _handleVideoTransition,
                      onMediaItemTap: _editMediaItem,
                      onMediaItemDelete: _deleteMediaItem,
                      onMediaItemResize: _resizeMediaItem,
                      onMediaItemMove: _moveMediaItem,
                      isTrimMode: _isTrimMode,
                      isCutMode: _isCutMode,
                      trimStart: _trimStart,
                      trimEnd: _trimEnd,
                      onTrimStartChanged: _updateTrimStart,
                      onTrimEndChanged: _updateTrimEnd,
                    ),
                  ),

                  // Media Tools
                  Container(
                    height: AppConstants.toolbarHeight,
                    color: Color(AppConstants.surfaceColor),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildToolButton(
                          icon: Icons.text_fields,
                          label: 'Text',
                          onPressed: _addText,
                        ),
                        _buildToolButton(
                          icon: Icons.image,
                          label: 'Image',
                          onPressed: _addImage,
                        ),
                        _buildToolButton(
                          icon: Icons.audiotrack,
                          label: 'Audio',
                          onPressed: _addAudio,
                        ),
                        _buildToolButton(
                          icon: Icons.videocam,
                          label: 'Video',
                          onPressed: _addVideo,
                        ),
                      ],
                    ),
                  ),

                  // Effects & Transform Tools
                  Container(
                    height: AppConstants.toolbarHeight,
                    color: Color(AppConstants.backgroundColor),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          _buildToolButton(
                            icon: Icons.filter,
                            label: 'Filter',
                            onPressed: _showFilterDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.transform,
                            label: 'Transform',
                            onPressed: _showTransformDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.layers,
                            label: 'Layers',
                            onPressed: _showLayerDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.crop,
                            label: 'Crop',
                            onPressed: _showCropDialog,
                          ),
                          _buildToolButton(
                            icon: Icons.copy,
                            label: 'Duplicate',
                            onPressed: _duplicateSelectedItem,
                          ),
                          _buildToolButton(
                            icon: Icons.volume_up,
                            label: 'Volume',
                            onPressed: _showVolumeDialog,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildEmptyCanvas() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[900]!,
            Colors.grey[800]!,
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 2,
          style: BorderStyle.solid,
        ),
      ),
      child: Stack(
        children: [
          // Grid pattern
          CustomPaint(
            painter: GridPainter(),
            size: Size.infinite,
          ),
          // Center content with proper constraints
          Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.video_library_outlined,
                      size: 48,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Empty Canvas',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      'Add videos, images, text, and audio to get started',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.5),
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),
                    // Responsive button layout
                    LayoutBuilder(
                      builder: (context, constraints) {
                        if (constraints.maxWidth > 300) {
                          // Wide layout - horizontal buttons
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildQuickAddButton(
                                icon: Icons.videocam,
                                label: 'Video',
                                onTap: _addVideo,
                                compact: true,
                              ),
                              const SizedBox(width: 12),
                              _buildQuickAddButton(
                                icon: Icons.image,
                                label: 'Image',
                                onTap: _addImage,
                                compact: true,
                              ),
                              const SizedBox(width: 12),
                              _buildQuickAddButton(
                                icon: Icons.text_fields,
                                label: 'Text',
                                onTap: _addText,
                                compact: true,
                              ),
                            ],
                          );
                        } else {
                          // Narrow layout - vertical buttons
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _buildQuickAddButton(
                                icon: Icons.videocam,
                                label: 'Add Video',
                                onTap: _addVideo,
                                compact: false,
                              ),
                              const SizedBox(height: 8),
                              _buildQuickAddButton(
                                icon: Icons.image,
                                label: 'Add Image',
                                onTap: _addImage,
                                compact: false,
                              ),
                              const SizedBox(height: 8),
                              _buildQuickAddButton(
                                icon: Icons.text_fields,
                                label: 'Add Text',
                                onTap: _addText,
                                compact: false,
                              ),
                            ],
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAddButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool compact = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: compact
            ? const EdgeInsets.all(8)
            : const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        constraints: compact
            ? const BoxConstraints(minWidth: 60, minHeight: 60)
            : const BoxConstraints(minWidth: 100, minHeight: 50),
        decoration: BoxDecoration(
          color: Color(AppConstants.accentColor).withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Color(AppConstants.accentColor).withValues(alpha: 0.5),
          ),
        ),
        child: compact
            ? Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    color: Color(AppConstants.accentColor),
                    size: 20,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    label,
                    style: TextStyle(
                      color: Color(AppConstants.accentColor),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    color: Color(AppConstants.accentColor),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      label,
                      style: TextStyle(
                        color: Color(AppConstants.accentColor),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  // Cache for overlay items to prevent rebuilding on every frame
  List<Widget>? _cachedOverlayItems;
  double _lastOverlayPosition = -1;
  String? _lastSelectedItemId;

  /// Invalidate overlay cache when media items change
  void _invalidateOverlayCache() {
    _cachedOverlayItems = null;
    _lastOverlayPosition = -1;
    _lastSelectedItemId = null;
  }

  List<Widget> _buildOverlayItems() {
    // Check if we need to rebuild overlay items
    if (_cachedOverlayItems != null &&
        _lastOverlayPosition == _currentPosition &&
        _lastSelectedItemId == _selectedItemId) {
      return _cachedOverlayItems!;
    }

    final overlayItems = <Widget>[];

    // Sort items by layer (lower layer numbers appear first/behind)
    final sortedItems = List<MediaItem>.from(_currentProject.mediaItems)
      ..sort((a, b) => a.layer.compareTo(b.layer));

    for (final item in sortedItems) {
      if (item.startTime <= _currentPosition &&
          _currentPosition <= item.endTime &&
          item.opacity > 0) {
        Widget? itemWidget;

        switch (item.type) {
          case MediaType.text:
            if (item.text != null) {
              itemWidget = Text(
                item.text!,
                style: TextStyle(
                  color: Color(item.textColor ?? 0xFFFFFFFF),
                  fontSize: item.fontSize ?? AppConstants.defaultTextSize,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                      color: Colors.black.withValues(alpha: 0.7),
                    ),
                  ],
                ),
              );
            }
            break;
          case MediaType.image:
            if (item.filePath != null) {
              itemWidget = SizedBox(
                width: item.width ?? 100,
                height: item.height ?? 100,
                child: Image.file(
                  File(item.filePath!),
                  fit: BoxFit.cover,
                ),
              );
            }
            break;
          case MediaType.video:
            // Skip all video items - they are handled by the main video player
            // Videos will be switched automatically based on timeline position
            break;
          default:
            break;
        }

        if (itemWidget != null) {
          // Apply transform if available
          if (item.transformSettings != null) {
            itemWidget = _applyTransform(itemWidget, item.transformSettings!);
          }

          // Apply filter if available
          if (item.filterSettings != null) {
            itemWidget = _applyFilter(itemWidget, item.filterSettings!);
          }

          // Apply opacity
          if (item.opacity < 1.0) {
            itemWidget = Opacity(
              opacity: item.opacity,
              child: itemWidget,
            );
          }

          // Wrap with ResizableWidget for interactive resize
          overlayItems.add(
            ResizableWidget(
              key:
                  ValueKey('${item.id}_${_currentPosition}_${_selectedItemId}'),
              mediaItem: item,
              isSelected: _selectedItemId == item.id,
              onTap: () {
                setState(() {
                  _selectedItemId = _selectedItemId == item.id ? null : item.id;
                });
              },
              onChanged: (updatedItem) {
                _updateMediaItemFromResize(updatedItem);
              },
              child: itemWidget,
            ),
          );
        }
      }
    }

    // Cache the result
    _cachedOverlayItems = overlayItems;
    _lastOverlayPosition = _currentPosition;
    _lastSelectedItemId = _selectedItemId;

    return overlayItems;
  }

  Widget _buildTrimIndicators() {
    return Stack(
      children: [
        // Start trim indicator
        if (_trimStart != null)
          Positioned(
            left: (_trimStart! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Color(AppConstants.accentColor),
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        // End trim indicator
        if (_trimEnd != null)
          Positioned(
            left: (_trimEnd! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Color(AppConstants.accentColor),
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCutIndicators() {
    return Stack(
      children: [
        // Cut indicators (similar to trim but different color)
        if (_trimStart != null)
          Positioned(
            left: (_trimStart! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Colors.red,
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.content_cut,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        if (_trimEnd != null)
          Positioned(
            left: (_trimEnd! / _videoDuration) *
                MediaQuery.of(context).size.width,
            top: 0,
            bottom: 0,
            child: Container(
              width: 3,
              color: Colors.red,
              child: const Align(
                alignment: Alignment.topCenter,
                child: Icon(
                  Icons.content_cut,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Audio management methods
  Future<void> _initializeAudioPlayers() async {
    final audioItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.audio && item.filePath != null)
        .toList();

    for (final audioItem in audioItems) {
      await _initializeAudioPlayer(audioItem);
    }
  }

  Future<void> _initializeAudioPlayer(MediaItem audioItem) async {
    if (!_audioPlayers.containsKey(audioItem.id) &&
        audioItem.filePath != null) {
      try {
        final player = AudioPlayer();
        await player.setSource(DeviceFileSource(audioItem.filePath!));
        await player.setVolume(audioItem.volume ?? 0.5);
        _audioPlayers[audioItem.id] = player;
      } catch (e) {
        print('Error initializing audio player for ${audioItem.id}: $e');
      }
    }
  }

  void _syncAudioPlayback() {
    // Skip audio sync if video is not playing to prevent conflicts
    if (!_isPlaying || !_videoController.value.isPlaying) {
      print('🔇 Skipping audio sync - video not playing');
      return;
    }

    // Run audio sync in background with minimal delay
    Future.microtask(() => _syncAudioPlaybackAsync());
  }

  void _syncAudioPlaybackAsync() {
    try {
      print(
          '🎵 Starting audio sync at position: ${_currentPosition.toStringAsFixed(1)}s');

      final audioItems = _currentProject.mediaItems
          .where(
              (item) => item.type == MediaType.audio && item.filePath != null)
          .toList();

      print('🎵 Found ${audioItems.length} audio items to sync');

      for (final audioItem in audioItems) {
        final player = _audioPlayers[audioItem.id];
        if (player == null) {
          print('🔇 No player found for audio item: ${audioItem.id}');
          continue;
        }

        // Check if audio should be playing at current position
        final shouldPlay = _isPlaying &&
            _currentPosition >= audioItem.startTime &&
            _currentPosition <= audioItem.endTime;

        if (shouldPlay && !_activeAudioIds.contains(audioItem.id)) {
          // Start playing audio (completely non-blocking)
          final relativePosition = _currentPosition - audioItem.startTime;
          print(
              '🎵 Starting audio ${audioItem.id} at relative position: ${relativePosition.toStringAsFixed(1)}s');

          // Use Future.microtask to ensure non-blocking
          Future.microtask(() async {
            try {
              await player.seek(
                  Duration(milliseconds: (relativePosition * 1000).round()));
              await player.resume();
              _activeAudioIds.add(audioItem.id);
              print('🎵 Audio ${audioItem.id} started successfully');

              // Update video volume when audio starts
              _updateVideoVolume();
            } catch (e) {
              print('🔇 Error starting audio ${audioItem.id}: $e');
            }
          });
        } else if (!shouldPlay && _activeAudioIds.contains(audioItem.id)) {
          // Stop playing audio (completely non-blocking)
          print('🔇 Stopping audio ${audioItem.id}');

          Future.microtask(() async {
            try {
              await player.pause();
              _activeAudioIds.remove(audioItem.id);
              print('🔇 Audio ${audioItem.id} stopped successfully');

              // Update video volume when audio stops
              _updateVideoVolume();
            } catch (e) {
              print('🔇 Error stopping audio ${audioItem.id}: $e');
            }
          });
        }
      }
    } catch (e) {
      print('🔇 Audio sync error (ignored): $e');
    }
  }

  Future<void> _pauseAllAudio() async {
    try {
      if (_audioPlayers.isEmpty) {
        print('🎵 No audio players to pause');
        return;
      }

      print('🎵 Pausing ${_audioPlayers.length} audio players...');

      // Pause each audio player individually with error handling
      final pauseResults = <Future<void>>[];

      for (final entry in _audioPlayers.entries) {
        final playerId = entry.key;
        final player = entry.value;

        pauseResults.add(player.pause().catchError((error) {
          print('⚠️ Error pausing audio player $playerId: $error');
          return null; // Continue with other players
        }));
      }

      // Wait for all pause operations to complete (with error tolerance)
      await Future.wait(pauseResults, eagerError: false);

      // Clear active audio IDs
      _activeAudioIds.clear();
      print('🎵 Active audio IDs cleared');

      // Update video volume when all audio is paused
      try {
        await _updateVideoVolume();
        print('🔊 Video volume updated after audio pause');
      } catch (e) {
        print('⚠️ Error updating video volume: $e');
      }
    } catch (e) {
      print('❌ Error in _pauseAllAudio: $e');
      // Still clear the active IDs even if some players failed to pause
      _activeAudioIds.clear();
    }
  }

  // Check if there are any audio items that should be playing at current position
  bool _hasActiveAudio() {
    final audioItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.audio && item.filePath != null)
        .toList();

    return audioItems.any((audioItem) =>
        _currentPosition >= audioItem.startTime &&
        _currentPosition <= audioItem.endTime);
  }

  // Get the appropriate video volume based on audio presence
  double _getVideoVolume(MediaItem videoItem) {
    if (_hasActiveAudio()) {
      // If there's active audio, reduce video volume significantly or mute it
      return videoItem.videoVolume * 0.1; // Reduce to 10% of original volume
    }
    return videoItem
        .videoVolume; // Use full video volume when no audio is playing
  }

  // Update video volume dynamically based on current audio state
  Future<void> _updateVideoVolume() async {
    if (_videoController.value.isInitialized) {
      // Find current active video item
      final videoItems = _currentProject.mediaItems
          .where(
              (item) => item.type == MediaType.video && item.filePath != null)
          .toList();

      if (videoItems.isNotEmpty) {
        // Find the video that should be playing at current position
        final activeVideo = videoItems.firstWhere(
          (item) =>
              _currentPosition >= item.startTime &&
              _currentPosition <= item.endTime,
          orElse: () => videoItems.first,
        );

        final newVolume = _getVideoVolume(activeVideo);
        await _videoController.setVolume(newVolume);
      }
    }
  }

  // Timer methods untuk memaksa video update
  void _startVideoUpdateTimer() {
    _stopVideoUpdateTimer();
    _videoUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isPlaying || !mounted || !_videoController.value.isInitialized) {
        _stopVideoUpdateTimer();
        return;
      }

      final newPosition =
          _videoController.value.position.inMilliseconds / 1000.0;
      if (newPosition != _currentPosition) {
        print(
            '🔄 Timer forcing video position update: ${newPosition.toStringAsFixed(2)}s');
        setState(() {
          _currentPosition = newPosition;
        });
      }
    });
  }

  void _stopVideoUpdateTimer() {
    _videoUpdateTimer?.cancel();
    _videoUpdateTimer = null;
  }

  // Restart video from beginning
  void _restartVideo() {
    print('🔄 Restarting video from beginning');
    _hasVideoEnded = false;
    _seekToPosition(0.0);

    if (!_isPlaying) {
      Future.delayed(const Duration(milliseconds: 500), () {
        _togglePlayPause();
      });
    }

    _showSuccessSnackBar('Video restarted');
  }

  // Stop video and audio completely
  void _stopVideoCompletely() {
    print('⏹️ Stopping video and audio completely');
    _isPlaying = false;
    _hasVideoEnded = true;
    _videoController.pause();
    _pauseAllAudio();
    _stopVideoUpdateTimer();

    setState(() {
      _currentPosition = 0.0;
    });

    // Seek to beginning
    if (_videoController.value.isInitialized) {
      _videoController.seekTo(Duration.zero);
    }

    _showSuccessSnackBar('Video stopped');
  }

  void _seekAllAudio(double position) {
    try {
      final audioItems = _currentProject.mediaItems
          .where(
              (item) => item.type == MediaType.audio && item.filePath != null)
          .toList();

      for (final audioItem in audioItems) {
        final player = _audioPlayers[audioItem.id];
        if (player == null) continue;

        if (position >= audioItem.startTime && position <= audioItem.endTime) {
          final relativePosition = position - audioItem.startTime;
          // Non-blocking seek
          player
              .seek(Duration(milliseconds: (relativePosition * 1000).round()));
        }
      }
    } catch (e) {
      // Ignore audio seek errors to prevent blocking timeline
    }
  }

  // Video control methods
  void _togglePlayPause() async {
    print('=== TOGGLE PLAY/PAUSE ===');
    print('Current video path: $_currentVideoPath');
    print('Video initialized: ${_videoController.value.isInitialized}');
    print('Current playing state: $_isPlaying');
    print('Video switching state: $_isSwitchingVideo');

    // Prevent toggle during video switching
    if (_isSwitchingVideo) {
      print('⚠️ Cannot toggle play/pause during video switching');
      return;
    }

    if (_currentVideoPath == null || !_videoController.value.isInitialized) {
      _showErrorSnackBar('No video loaded. Add a video first.');
      return;
    }

    try {
      if (_isPlaying) {
        // Pause video
        print('Pausing video...');

        // Update state first to prevent race conditions
        setState(() {
          _isPlaying = false;
        });

        // Pause video controller
        await _videoController.pause();

        // Pause all audio
        await _pauseAllAudio();

        // Stop video update timer
        _stopVideoUpdateTimer();

        print('Video paused successfully, _isPlaying = $_isPlaying');
      } else {
        // Play video
        print('Playing video...');

        // Reset video ended flag
        _hasVideoEnded = false;

        // Ensure video is at correct position before playing
        if (_videoController.value.isInitialized) {
          final currentPos =
              Duration(milliseconds: (_currentPosition * 1000).round());
          print(
              'Setting video position to: ${_currentPosition.toStringAsFixed(1)}s before play');

          // Seek to position first
          await _videoController.seekTo(currentPos);
          print('Video seeked successfully');

          // Start playing
          await _videoController.play();
          print('Video play() completed');

          // Update state after successful play
          setState(() {
            _isPlaying = true;
          });

          print('Video playing, _isPlaying = $_isPlaying');
          print(
              'Video controller playing: ${_videoController.value.isPlaying}');

          // Start timer untuk memaksa video update
          _startVideoUpdateTimer();

          // Sync audio after video is stable (shorter delay for better responsiveness)
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_isPlaying && _videoController.value.isPlaying && mounted) {
              print('🎵 Starting delayed audio sync after video stabilized');
              _syncAudioPlayback();
            }
          });
        } else {
          print('Video controller not initialized!');
          _showErrorSnackBar('Video controller not ready. Please try again.');
        }
      }
    } catch (e) {
      print('❌ Error in _togglePlayPause: $e');

      // Reset state on error
      setState(() {
        _isPlaying = false;
      });

      _stopVideoUpdateTimer();
      _showErrorSnackBar('Error controlling video playback: $e');
    }
  }

  void _seekBackward() {
    final newPosition = (_currentPosition - 10).clamp(0.0, _videoDuration);
    _seekToPosition(newPosition);
  }

  void _seekForward() {
    final newPosition = (_currentPosition + 10).clamp(0.0, _videoDuration);
    _seekToPosition(newPosition);
  }

  void _seekToPosition(double position) {
    // Skip if currently switching videos
    if (_isSwitchingVideo) return;

    print('Seeking to position: ${position.toStringAsFixed(1)}s');

    setState(() {
      _currentPosition = position;
    });

    // Seek audio to new position (non-blocking)
    _seekAllAudio(position);

    // Adjust position if in cut mode
    double adjustedPosition = position;

    if (_isCutMode && _trimStart != null && _trimEnd != null) {
      adjustedPosition = _getAdjustedPositionForCutMode(position);
    }

    // Always seek current video controller if initialized
    if (_videoController.value.isInitialized) {
      final duration = Duration(milliseconds: (position * 1000).round());
      _videoController.seekTo(duration).then((_) {
        print('Video seeked to: ${position.toStringAsFixed(1)}s');
      });
    }

    // Find the video that should be playing at this position
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .where((item) =>
            adjustedPosition >= item.startTime &&
            adjustedPosition <= item.endTime)
        .toList();

    if (videoItems.isNotEmpty) {
      // Sort by layer to get the topmost video
      videoItems.sort((a, b) => b.layer.compareTo(a.layer));
      final activeVideo = videoItems.first;

      // Switch video controller if needed
      if (activeVideo.filePath != _currentVideoPath && !_isSwitchingVideo) {
        _switchVideoController(activeVideo.filePath!, activeVideo);
      }
    }

    // Sync audio playback after seeking (longer delay to let video stabilize)
    if (_isPlaying) {
      Future.delayed(const Duration(milliseconds: 800), () {
        if (_isPlaying && _videoController.value.isPlaying) {
          print('🎵 Starting delayed audio sync after seek');
          _syncAudioPlayback();
          // Update video volume after seeking
          _updateVideoVolume();
        }
      });
    }
  }

  // Helper method to adjust position for cut mode
  double _getAdjustedPositionForCutMode(double timelinePosition) {
    if (!_isCutMode || _trimStart == null || _trimEnd == null) {
      return timelinePosition;
    }

    final cutStart = _trimStart!;
    final cutEnd = _trimEnd!;
    final cutDuration = cutEnd - cutStart;

    if (timelinePosition < cutStart) {
      // Position is in the first segment - no adjustment needed
      return timelinePosition;
    } else {
      // Position is in the second segment - add back the cut duration
      return timelinePosition + cutDuration;
    }
  }

  void _stopVideo() async {
    try {
      // Update state first to prevent race conditions
      setState(() {
        _isPlaying = false;
      });

      // Stop video update timer
      _stopVideoUpdateTimer();

      // Pause video if available
      if (_currentVideoPath != null && _videoController.value.isInitialized) {
        await _videoController.pause();
      }

      // Pause all audio
      await _pauseAllAudio();

      // Seek to beginning
      _seekToPosition(0.0);

      print('Video stopped successfully');
    } catch (e) {
      print('Error stopping video: $e');
      // Ensure state is consistent even if operations fail
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _skipToStart() {
    _seekToPosition(0.0);
  }

  void _skipToEnd() {
    _seekToPosition(_videoDuration);
  }

  // Trim and Cut methods
  void _toggleTrimMode() {
    // Check if we have any video to trim
    String? videoPath = _getActiveVideoPath();
    if (videoPath == null && !_isTrimMode) {
      _showErrorSnackBar('Please load a video first before using trim mode');
      return;
    }

    setState(() {
      _isTrimMode = !_isTrimMode;
      _isCutMode = false;
      if (_isTrimMode) {
        _trimStart = _currentPosition;
        _trimEnd = (_currentPosition + 5).clamp(0.0, _videoDuration);
        _showSuccessSnackBar(
            'Trim mode activated. Drag the blue markers to set trim points.');
      } else {
        _trimStart = null;
        _trimEnd = null;
        _showSuccessSnackBar('Trim mode deactivated.');
      }
    });
  }

  void _toggleCutMode() {
    // Simple cut: immediately cut at current position
    _performSimpleCut();
  }

  void _performSimpleCut() async {
    // Check if we have any video to cut
    String? videoPath = _getActiveVideoPath();
    if (videoPath == null) {
      _showErrorSnackBar('Please load a video first before cutting');
      return;
    }

    // Show confirmation dialog
    bool? shouldCut = await _showCutConfirmationDialog();
    if (shouldCut != true) return;

    setState(() {
      _isExporting = true;
    });

    try {
      // Cut the video at current position (split into two parts)
      String? outputPath = await _videoService.cutVideoAtPosition(
        inputPath: videoPath,
        cutPosition: _currentPosition,
      );

      if (outputPath != null) {
        _showSuccessSnackBar('Video cut successfully! Split into two parts.');

        // Optionally reload the first part
        // Navigator.pushReplacement(context, MaterialPageRoute(
        //   builder: (context) => EditorScreen(videoPath: outputPath),
        // ));
      } else {
        _showErrorSnackBar('Failed to cut video');
      }
    } catch (e) {
      _showErrorSnackBar('Error cutting video: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<bool?> _showCutConfirmationDialog() {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Color(AppConstants.surfaceColor),
          title: Row(
            children: [
              Icon(Icons.call_split, color: Colors.red, size: 24),
              SizedBox(width: 8),
              Text(
                'Cut Video',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Cut video at current position?',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Position: ${_formatTime(_currentPosition)}',
                      style: TextStyle(
                          color: Colors.blue, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'This will split your video into two separate files.',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.call_split, size: 16),
                  SizedBox(width: 4),
                  Text('Cut Here'),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _updateTrimStart(double newStart) {
    setState(() {
      _trimStart = newStart;
    });
  }

  void _updateTrimEnd(double newEnd) {
    setState(() {
      _trimEnd = newEnd;
    });
  }

  void _applyTrimCut() async {
    if (_trimStart == null || _trimEnd == null) {
      _showErrorSnackBar('Please set trim/cut points first');
      return;
    }

    // Get the video path from multiple sources
    String? videoPath = _getActiveVideoPath();

    if (videoPath == null) {
      _showErrorSnackBar('No video loaded to trim/cut');
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      String? outputPath;

      if (_isTrimMode) {
        outputPath = await _videoService.trimVideo(
          inputPath: videoPath,
          startTime: _trimStart!,
          endTime: _trimEnd!,
        );
        _showSuccessSnackBar('Video trimmed successfully!');
      } else if (_isCutMode) {
        outputPath = await _videoService.cutVideo(
          inputPath: videoPath,
          cutStart: _trimStart!,
          cutEnd: _trimEnd!,
        );
        _showSuccessSnackBar('Video cut successfully!');
      }

      if (outputPath != null) {
        // Reset trim/cut mode
        _resetTrimCut();

        // Optionally, you could load the new video
        // Navigator.pushReplacement(context, MaterialPageRoute(
        //   builder: (context) => EditorScreen(videoPath: outputPath),
        // ));
      } else {
        _showErrorSnackBar('Failed to process video');
      }
    } catch (e) {
      _showErrorSnackBar('Error processing video: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  void _resetTrimCut() {
    setState(() {
      _isTrimMode = false;
      _isCutMode = false;
      _isPreviewMode = false;
      _trimStart = null;
      _trimEnd = null;
    });

    // Reset temporary edits for all video items
    _resetAllTemporaryEdits();
    _showSuccessSnackBar('Trim/Cut cancelled');
  }

  // Enhanced temporary editing methods
  void _resetAllTemporaryEdits() {
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video)
        .toList();

    for (final item in videoItems) {
      _tempVideoService.resetTemporaryEdits(item.id);
    }
  }

  void _togglePreviewMode() {
    setState(() {
      _isPreviewMode = !_isPreviewMode;
    });

    if (_isPreviewMode) {
      _showSuccessSnackBar('Preview mode activated - showing temporary edits');
    } else {
      _showSuccessSnackBar('Preview mode deactivated');
    }
  }

  void _applyTemporaryTrim() async {
    if (_trimStart == null || _trimEnd == null) {
      _showErrorSnackBar('Please set trim points first');
      return;
    }

    final videoPath = _getActiveVideoPath();
    if (videoPath == null) {
      _showErrorSnackBar('No video loaded to trim');
      return;
    }

    try {
      // Find the main video item
      final videoItem = _currentProject.mediaItems.firstWhere(
          (item) => item.type == MediaType.video && item.filePath == videoPath);

      // Create or update temporary edit state
      final tempState = _tempVideoService.createTemporaryEditState(
        mediaItemId: videoItem.id,
        originalFilePath: videoPath,
        originalDuration: _videoDuration,
      );

      // Apply temporary trim
      _tempVideoService.applyTemporaryTrim(
        mediaItemId: videoItem.id,
        trimStart: _trimStart!,
        trimEnd: _trimEnd!,
      );

      // Update the media item with temporary state
      final updatedItem = videoItem.copyWith(
        temporaryEditState: tempState.copyWith(
          trimStart: _trimStart,
          trimEnd: _trimEnd,
          isActive: true,
        ),
      );

      // Update project
      final updatedItems = _currentProject.mediaItems.map((item) {
        return item.id == videoItem.id ? updatedItem : item;
      }).toList();

      setState(() {
        _currentProject = _currentProject.copyWith(mediaItems: updatedItems);
        _isTrimMode = false;
        _trimStart = null;
        _trimEnd = null;
      });

      _showSuccessSnackBar(
          'Temporary trim applied! Use preview to see result.');
    } catch (e) {
      _showErrorSnackBar('Error applying temporary trim: $e');
    }
  }

  void _applyTemporaryCut() async {
    if (_trimStart == null || _trimEnd == null) {
      _showErrorSnackBar('Please set cut points first');
      return;
    }

    final videoPath = _getActiveVideoPath();
    if (videoPath == null) {
      _showErrorSnackBar('No video loaded to cut');
      return;
    }

    try {
      // Find the main video item
      final videoItem = _currentProject.mediaItems.firstWhere(
          (item) => item.type == MediaType.video && item.filePath == videoPath);

      // Create or update temporary edit state
      final tempState = _tempVideoService.createTemporaryEditState(
        mediaItemId: videoItem.id,
        originalFilePath: videoPath,
        originalDuration: _videoDuration,
      );

      // Apply temporary cut
      _tempVideoService.applyTemporaryCut(
        mediaItemId: videoItem.id,
        cutStart: _trimStart!,
        cutEnd: _trimEnd!,
      );

      // Update the media item with temporary state
      final updatedItem = videoItem.copyWith(
        temporaryEditState: tempState.copyWith(
          cutPoints: [
            CutPoint(
              id: 'cut_${DateTime.now().millisecondsSinceEpoch}',
              startTime: _trimStart!,
              endTime: _trimEnd!,
            )
          ],
          isActive: true,
        ),
      );

      // Update project
      final updatedItems = _currentProject.mediaItems.map((item) {
        return item.id == videoItem.id ? updatedItem : item;
      }).toList();

      setState(() {
        _currentProject = _currentProject.copyWith(mediaItems: updatedItems);
        _isCutMode = false;
        _trimStart = null;
        _trimEnd = null;
      });

      _showSuccessSnackBar('Temporary cut applied! Use preview to see result.');
    } catch (e) {
      _showErrorSnackBar('Error applying temporary cut: $e');
    }
  }

  void _previewTemporaryEdits() async {
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video)
        .toList();

    if (videoItems.isEmpty) {
      _showErrorSnackBar('No video items to preview');
      return;
    }

    bool hasEdits = false;
    for (final item in videoItems) {
      if (_tempVideoService.hasActiveTemporaryEdits(item.id)) {
        hasEdits = true;
        break;
      }
    }

    if (!hasEdits) {
      _showErrorSnackBar('No temporary edits to preview');
      return;
    }

    // Show preview dialog
    _showPreviewDialog();
  }

  void _showPreviewDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Color(AppConstants.surfaceColor),
          title: Row(
            children: [
              Icon(Icons.preview, color: Colors.orange, size: 24),
              SizedBox(width: 8),
              Text(
                'Preview Temporary Edits',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Preview shows how your video will look with temporary edits applied.',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
              SizedBox(height: 16),
              _buildPreviewSummary(),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Close', style: TextStyle(color: Colors.white)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _togglePreviewMode();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
              ),
              child: Text('Enable Preview'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPreviewSummary() {
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: videoItems.map((item) {
        if (!_tempVideoService.hasActiveTemporaryEdits(item.id)) {
          return SizedBox.shrink();
        }

        final preview = _tempVideoService.previewTemporaryEdits(item.id);
        return FutureBuilder<Map<String, dynamic>>(
          future: preview,
          builder: (context, snapshot) {
            if (!snapshot.hasData) return SizedBox.shrink();

            final data = snapshot.data!;
            return Container(
              margin: EdgeInsets.only(bottom: 8),
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(AppConstants.backgroundColor),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Video: ${item.filePath?.split('/').last ?? 'Unknown'}',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Original: ${data['originalDuration'].toStringAsFixed(1)}s',
                    style: TextStyle(color: Colors.white70, fontSize: 11),
                  ),
                  Text(
                    'After edits: ${data['effectiveDuration'].toStringAsFixed(1)}s',
                    style: TextStyle(color: Colors.orange, fontSize: 11),
                  ),
                ],
              ),
            );
          },
        );
      }).toList(),
    );
  }

  // Helper method to get active video path from multiple sources
  String? _getActiveVideoPath() {
    // Priority 1: Current video path (from video switching)
    if (_currentVideoPath != null) {
      print('DEBUG: Using current video path: $_currentVideoPath');
      return _currentVideoPath;
    }

    // Priority 2: Original widget video path
    if (widget.videoPath != null) {
      print('DEBUG: Using widget video path: ${widget.videoPath}');
      return widget.videoPath;
    }

    // Priority 3: Find video from current project media items
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .toList();

    print('DEBUG: Found ${videoItems.length} video items in project');

    if (videoItems.isNotEmpty) {
      // Find video that contains current position
      final activeVideo = videoItems.firstWhere(
        (item) =>
            _currentPosition >= item.startTime &&
            _currentPosition <= item.endTime,
        orElse: () => videoItems.first, // Fallback to first video
      );
      print('DEBUG: Using project video path: ${activeVideo.filePath}');
      return activeVideo.filePath;
    }

    print('DEBUG: No video path found');
    return null;
  }

  String _formatTime(double seconds) {
    final duration = Duration(seconds: seconds.round());
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    }
  }

  // Media editing methods
  void _addText() {
    showDialog(
      context: context,
      builder: (context) => TextOverlayWidget(
        onTextUpdated: (textItem) {
          // Validate time values before adding
          final validatedItem = _validateTextItemTimes(textItem);

          // Ensure text gets the correct layer
          final updatedTextItem = validatedItem.copyWith(
            layer: _getNextLayerForType(MediaType.text),
          );

          setState(() {
            _currentProject = _currentProject.copyWith(
              mediaItems: [..._currentProject.mediaItems, updatedTextItem],
              updatedAt: DateTime.now(),
            );
          });
          Navigator.pop(context);
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  /// Validate and fix text item time values
  MediaItem _validateTextItemTimes(MediaItem textItem) {
    double startTime = textItem.startTime.clamp(0.0, 59.0);
    double endTime = textItem.endTime;

    // Ensure end time is at least 1 second after start time
    final minEndTime = startTime + 1.0;
    endTime = endTime.clamp(minEndTime, 60.0);

    // If end time would exceed max, adjust start time
    if (endTime >= 60.0 && startTime >= 59.0) {
      startTime = 58.0;
      endTime = 60.0;
    }

    return textItem.copyWith(
      startTime: startTime,
      endTime: endTime,
    );
  }

  Future<void> _addImage() async {
    try {
      final XFile? image =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        final fileName = image.path.split('/').last;

        final imageItem = MediaItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: MediaType.image,
          filePath: image.path,
          startTime: _currentPosition,
          endTime: (_currentPosition + 5).clamp(0.0, _videoDuration),
          x: 50,
          y: 50,
          width: 150,
          height: 150,
          layer: _getNextLayerForType(MediaType.image),
        );

        setState(() {
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, imageItem],
            updatedAt: DateTime.now(),
          );
        });

        _showSuccessSnackBar('Image "$fileName" added successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding image: $e');
    }
  }

  Future<void> _addAudio() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        final fileName = file.name;

        // Validate file extension
        final extension = fileName.split('.').last.toLowerCase();
        if (!AppConstants.supportedAudioFormats.contains(extension)) {
          _showErrorSnackBar(
              'Unsupported audio format. Supported formats: ${AppConstants.supportedAudioFormats.join(', ')}');
          return;
        }

        final audioItem = MediaItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: MediaType.audio,
          filePath: file.path!,
          startTime: _currentPosition,
          endTime: _videoDuration,
          volume: 0.5,
          layer: _getNextLayerForType(MediaType.audio),
        );

        setState(() {
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, audioItem],
            updatedAt: DateTime.now(),
          );
        });

        // Initialize audio player for the new audio item
        await _initializeAudioPlayer(audioItem);

        _showSuccessSnackBar('Audio "$fileName" added successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding audio: $e');
    }
  }

  Future<void> _addVideo() async {
    try {
      final XFile? video =
          await _imagePicker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        // Get video duration
        final controller = VideoPlayerController.file(File(video.path));
        await controller.initialize();
        final videoDuration = controller.value.duration.inMilliseconds / 1000.0;
        controller.dispose();

        // Calculate start time - place after the last video
        double startTime = 0.0;
        final videoItems = _currentProject.mediaItems
            .where((item) => item.type == MediaType.video)
            .toList();

        if (videoItems.isNotEmpty) {
          // Find the latest end time of existing videos
          final latestEndTime = videoItems
              .map((item) => item.endTime)
              .reduce((a, b) => a > b ? a : b);
          startTime = latestEndTime;
        }

        final endTime = startTime + videoDuration;

        // Update project duration if needed
        final newProjectDuration =
            endTime > _videoDuration ? endTime : _videoDuration;

        final videoItem = MediaItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: MediaType.video,
          filePath: video.path,
          startTime: startTime,
          endTime: endTime,
          videoVolume: 1.0, // Default video volume
          layer: _getNextLayerForType(MediaType.video),
          // No x, y, width, height for videos - they fill the entire preview
        );

        setState(() {
          _videoDuration = newProjectDuration;
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, videoItem],
            duration: newProjectDuration,
            updatedAt: DateTime.now(),
          );
        });

        // If this is the first video in an empty project, initialize video controller
        if (widget.videoPath == null && videoItems.isEmpty) {
          await _initializeFirstVideo(video.path);
        }

        // Seek to the start of the new video to show it in preview
        _seekToPosition(startTime);

        _showSuccessSnackBar('Video added successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding video: $e');
    }
  }

  void _editMediaItem(MediaItem item) {
    if (item.type == MediaType.text) {
      showDialog(
        context: context,
        builder: (context) => TextOverlayWidget(
          textItem: item,
          onTextUpdated: (updatedItem) {
            setState(() {
              final items = _currentProject.mediaItems.map((i) {
                return i.id == item.id ? updatedItem : i;
              }).toList();

              _currentProject = _currentProject.copyWith(
                mediaItems: items,
                updatedAt: DateTime.now(),
              );
            });
            Navigator.pop(context);
          },
          onCancel: () => Navigator.pop(context),
        ),
      );
    }
  }

  void _deleteMediaItem(MediaItem item) async {
    // Pause playback before deletion for better UX
    await _pausePlaybackForDeletion();

    setState(() {
      final items =
          _currentProject.mediaItems.where((i) => i.id != item.id).toList();
      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });

    // Check if we deleted a video and handle preview reset
    if (item.type == MediaType.video) {
      _handleVideoItemDeletion();
    }
  }

  /// Delete media item with undo support
  void _deleteMediaItemWithUndo(MediaItem item) async {
    // Pause playback before deletion for better UX
    await _pausePlaybackForDeletion();

    final action = custom_undo.DeleteMediaItemAction(
      item: item,
      mediaItems: _currentProject.mediaItems,
      onRestore: (restoredItem) {
        setState(() {
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, restoredItem],
            updatedAt: DateTime.now(),
          );
        });
        _showSuccessSnackBar('Item restored');

        // If restored item is a video, reinitialize video controller
        if (restoredItem.type == MediaType.video) {
          _handleVideoRestoration();
        }
      },
    );

    _undoManager.executeAction(action);

    setState(() {
      _selectedItemId = null;
      _selectedItemIds.clear();
    });

    // Check if we deleted a video and handle preview reset
    if (item.type == MediaType.video) {
      _handleVideoItemDeletion();
    }

    _showSuccessSnackBar('Item deleted (Ctrl+Z to undo)');
  }

  /// Delete multiple selected items
  void _deleteMultipleItems() async {
    if (_selectedItemIds.isEmpty) return;

    final itemsToDelete = _currentProject.mediaItems
        .where((item) => _selectedItemIds.contains(item.id))
        .toList();

    if (itemsToDelete.isEmpty) return;

    // Pause playback before deletion for better UX
    await _pausePlaybackForDeletion();

    // Check if any video items will be deleted
    final hasVideoItems =
        itemsToDelete.any((item) => item.type == MediaType.video);

    final action = custom_undo.DeleteMultipleItemsAction(
      items: itemsToDelete,
      mediaItems: _currentProject.mediaItems,
      onRestore: (restoredItems) {
        setState(() {
          _currentProject = _currentProject.copyWith(
            mediaItems: [..._currentProject.mediaItems, ...restoredItems],
            updatedAt: DateTime.now(),
          );
        });
        _showSuccessSnackBar('${restoredItems.length} items restored');

        // If any restored items are videos, reinitialize video controller
        if (restoredItems.any((item) => item.type == MediaType.video)) {
          _handleVideoRestoration();
        }
      },
    );

    _undoManager.executeAction(action);

    setState(() {
      _selectedItemId = null;
      _selectedItemIds.clear();
    });

    // Check if we deleted video items and handle preview reset
    if (hasVideoItems) {
      _handleVideoItemDeletion();
    }

    _showSuccessSnackBar(
        '${itemsToDelete.length} items deleted (Ctrl+Z to undo)');
  }

  /// Undo last action
  void _undo() {
    if (_undoManager.undo()) {
      _showSuccessSnackBar(
          'Undo: ${_undoManager.redoDescription ?? 'Action undone'}');
    } else {
      _showErrorSnackBar('Nothing to undo');
    }
  }

  /// Redo last undone action
  void _redo() {
    if (_undoManager.redo()) {
      _showSuccessSnackBar(
          'Redo: ${_undoManager.undoDescription ?? 'Action redone'}');
    } else {
      _showErrorSnackBar('Nothing to redo');
    }
  }

  /// Handle video item deletion - reset preview if no videos remain
  void _handleVideoItemDeletion() {
    // Check if there are any remaining video items
    final remainingVideoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .toList();

    if (remainingVideoItems.isEmpty) {
      // No videos left, reset to empty canvas
      _resetToEmptyCanvas();
    } else {
      // There are still videos, switch to the first available one
      final firstVideo = remainingVideoItems.first;
      if (firstVideo.filePath != _currentVideoPath) {
        _switchVideoController(firstVideo.filePath!, firstVideo);
      }
    }
  }

  /// Handle video restoration after undo
  void _handleVideoRestoration() {
    // Find the first video item to restore
    final videoItems = _currentProject.mediaItems
        .where((item) => item.type == MediaType.video && item.filePath != null)
        .toList();

    if (videoItems.isNotEmpty) {
      final firstVideo = videoItems.first;
      // If we don't have a current video or it's different, switch to it
      if (_currentVideoPath == null ||
          _currentVideoPath != firstVideo.filePath) {
        _switchVideoController(firstVideo.filePath!, firstVideo);
      }
    }
  }

  /// Reset preview to empty canvas state
  void _resetToEmptyCanvas() async {
    try {
      // Stop and dispose current video controller if it exists and is initialized
      if (_currentVideoPath != null && _videoController.value.isInitialized) {
        _videoController.removeListener(_videoListener);
        await _videoController.pause();
        await _videoController.dispose();

        // Create a new empty video controller to prevent errors
        _videoController = VideoPlayerController.file(File(''));
      }

      // Reset video-related state
      setState(() {
        _currentVideoPath = null;
        _isPlaying = false;
        _currentPosition = 0.0;
        _selectedItemId = null;
      });

      // Stop any playing audio
      await _pauseAllAudio();

      _showSuccessSnackBar('Preview reset - no videos remaining');
    } catch (e) {
      // Silently handle errors during reset
      setState(() {
        _currentVideoPath = null;
        _isPlaying = false;
        _currentPosition = 0.0;
        _selectedItemId = null;
      });
    }
  }

  /// Pause playback before deletion for better user experience
  Future<void> _pausePlaybackForDeletion() async {
    try {
      print('🔇 Pausing playback before deletion...');

      // Store the current playing state
      final wasPlaying = _isPlaying;

      // Update state first to prevent race conditions
      if (_isPlaying) {
        setState(() {
          _isPlaying = false;
        });
      }

      // Stop video update timer to prevent conflicts
      _stopVideoUpdateTimer();

      // Pause video controller if available and initialized
      if (_currentVideoPath != null && _videoController.value.isInitialized) {
        try {
          await _videoController.pause();
          print('🎬 Video controller paused successfully');
        } catch (e) {
          print('⚠️ Error pausing video controller: $e');
        }
      }

      // Pause all audio players
      try {
        await _pauseAllAudio();
        print('🎵 All audio paused successfully');
      } catch (e) {
        print('⚠️ Error pausing audio: $e');
      }
      // Small delay to give user visual feedback that playback stopped
      if (wasPlaying) {
        await Future.delayed(const Duration(milliseconds: 300));
        print('🔇 Pause feedback delay completed');
      }
    } catch (e) {
      print('❌ Error in _pausePlaybackForDeletion: $e');
      // Ensure state is consistent even if operations fail
      setState(() {
        _isPlaying = false;
      });
      _stopVideoUpdateTimer();
    }
  }

  Future<void> _exportVideo() async {
    if (_isExporting) return;

    // Check if project has any media
    if (_currentProject.mediaItems.isEmpty) {
      _showErrorSnackBar('Cannot export empty project. Add some media first.');
      return;
    }

    setState(() {
      _isExporting = true;
    });

    try {
      String? outputPath;

      // Apply trim or cut if active and we have a main video
      if (_isTrimMode &&
          _trimStart != null &&
          _trimEnd != null &&
          widget.videoPath != null) {
        outputPath = await _videoService.trimVideo(
          inputPath: widget.videoPath!,
          startTime: _trimStart!,
          endTime: _trimEnd!,
        );
      } else if (_isCutMode &&
          _trimStart != null &&
          _trimEnd != null &&
          widget.videoPath != null) {
        outputPath = await _videoService.cutVideo(
          inputPath: widget.videoPath!,
          cutStart: _trimStart!,
          cutEnd: _trimEnd!,
        );
      } else {
        // Export with all media items
        outputPath = await _videoService.exportProject(_currentProject);
      }

      if (outputPath != null) {
        _showSuccessSnackBar('Video exported successfully to: $outputPath');
      } else {
        _showErrorSnackBar('Failed to export video');
      }
    } catch (e) {
      _showErrorSnackBar('Error exporting video: $e');
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  void _saveProject() {
    // TODO: Implement project saving to local storage
    _showSuccessSnackBar('Project saved successfully');
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  // New methods for filter, transform, and multi-layer features
  void _showFilterDialog() {
    // Get currently selected media item or apply to main video
    MediaItem? selectedItem = _getSelectedMediaItem();

    showDialog(
      context: context,
      builder: (context) => FilterWidget(
        currentFilter: selectedItem?.filterSettings,
        onFilterChanged: (filterSettings) {
          if (selectedItem != null) {
            _updateMediaItemFilter(selectedItem, filterSettings);
          } else {
            _showErrorSnackBar('Please select a media item to apply filter');
          }
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showTransformDialog() {
    MediaItem? selectedItem = _getSelectedMediaItem();

    if (selectedItem == null) {
      _showErrorSnackBar('Please select a media item to transform');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => TransformWidget(
        currentTransform: selectedItem.transformSettings,
        onTransformChanged: (transformSettings) {
          _updateMediaItemTransform(selectedItem, transformSettings);
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showLayerDialog() {
    showDialog(
      context: context,
      builder: (context) => MultiLayerWidget(
        mediaItems: _currentProject.mediaItems,
        onLayersChanged: (updatedItems) {
          setState(() {
            _currentProject = _currentProject.copyWith(
              mediaItems: updatedItems,
              updatedAt: DateTime.now(),
            );
          });
        },
        onItemSelected: (item) {
          // Could implement item selection highlighting here
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showCropDialog() {
    MediaItem? selectedItem = _getSelectedMediaItem();

    if (selectedItem == null) {
      _showErrorSnackBar('Please select a media item to crop');
      return;
    }

    // Only allow crop for visual items (image, video, text)
    if (selectedItem.type == MediaType.audio) {
      _showErrorSnackBar('Cannot crop audio items');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => CropWidget(
        mediaItem: selectedItem,
        onCropChanged: (updatedItem) {
          setState(() {
            final items = _currentProject.mediaItems.map((i) {
              return i.id == selectedItem.id ? updatedItem : i;
            }).toList();

            _currentProject = _currentProject.copyWith(
              mediaItems: items,
              updatedAt: DateTime.now(),
            );
          });
          _showSuccessSnackBar('Item cropped successfully');
        },
        onCancel: () => Navigator.pop(context),
      ),
    );
  }

  void _showVolumeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Color(AppConstants.surfaceColor),
        title: const Text(
          'Volume Control',
          style: TextStyle(color: Colors.white),
        ),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Video Volume Control
              const Text(
                'Video Volume',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              const SizedBox(height: 8),
              StatefulBuilder(
                builder: (context, setDialogState) {
                  // Get current video volume
                  double currentVideoVolume = 1.0;
                  final videoItems = _currentProject.mediaItems
                      .where((item) => item.type == MediaType.video)
                      .toList();

                  if (videoItems.isNotEmpty) {
                    // Find active video at current position
                    final activeVideo = videoItems.firstWhere(
                      (item) =>
                          _currentPosition >= item.startTime &&
                          _currentPosition <= item.endTime,
                      orElse: () => videoItems.first,
                    );
                    currentVideoVolume = activeVideo.videoVolume;
                  }

                  return Column(
                    children: [
                      Slider(
                        value: currentVideoVolume,
                        min: 0.0,
                        max: 1.0,
                        divisions: 20,
                        label: '${(currentVideoVolume * 100).round()}%',
                        activeColor: Color(AppConstants.accentColor),
                        onChanged: (value) {
                          setDialogState(() {
                            // Update all video items' volume
                            final updatedItems =
                                _currentProject.mediaItems.map((item) {
                              if (item.type == MediaType.video) {
                                return item.copyWith(videoVolume: value);
                              }
                              return item;
                            }).toList();

                            setState(() {
                              _currentProject = _currentProject.copyWith(
                                mediaItems: updatedItems,
                                updatedAt: DateTime.now(),
                              );
                            });

                            // Update video controller volume immediately
                            _updateVideoVolume();
                          });
                        },
                      ),
                      Text(
                        '${(currentVideoVolume * 100).round()}%',
                        style: const TextStyle(color: Colors.white70),
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Note: Video volume automatically reduces when audio is playing',
                style: TextStyle(color: Colors.white70, fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _duplicateSelectedItem() {
    MediaItem? selectedItem = _getSelectedMediaItem();

    if (selectedItem == null) {
      _showErrorSnackBar('Please select a media item to duplicate');
      return;
    }

    final duplicatedItem = selectedItem.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: selectedItem.endTime,
      endTime: selectedItem.endTime +
          (selectedItem.endTime - selectedItem.startTime),
      layer: _getNextAvailableLayer(),
    );

    setState(() {
      _currentProject = _currentProject.copyWith(
        mediaItems: [..._currentProject.mediaItems, duplicatedItem],
        updatedAt: DateTime.now(),
      );
    });

    _showSuccessSnackBar('Media item duplicated');
  }

  MediaItem? _getSelectedMediaItem() {
    if (_selectedItemId != null) {
      return _currentProject.mediaItems
          .where((item) => item.id == _selectedItemId)
          .firstOrNull;
    }

    // Fallback: return the last added item or null
    if (_currentProject.mediaItems.isNotEmpty) {
      return _currentProject.mediaItems.last;
    }
    return null;
  }

  void _updateMediaItemFilter(MediaItem item, FilterSettings? filterSettings) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id ? i.copyWith(filterSettings: filterSettings) : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });

    _showSuccessSnackBar('Filter applied');
  }

  void _updateMediaItemTransform(
      MediaItem item, TransformSettings? transformSettings) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id
            ? i.copyWith(transformSettings: transformSettings)
            : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });

    _showSuccessSnackBar('Transform applied');
  }

  int _getNextAvailableLayer() {
    if (_currentProject.mediaItems.isEmpty) return 0;

    final maxLayer = _currentProject.mediaItems
        .map((item) => item.layer)
        .reduce((a, b) => a > b ? a : b);

    return maxLayer + 1;
  }

  int _getNextLayerForType(MediaType type) {
    // Get the highest layer for this media type
    final itemsOfType =
        _currentProject.mediaItems.where((item) => item.type == type).toList();

    if (itemsOfType.isEmpty) {
      // First item of this type, assign based on type priority
      switch (type) {
        case MediaType.video:
          return 0; // Video starts at layer 0
        case MediaType.audio:
          return 100; // Audio starts at layer 100 (separate from video layers)
        case MediaType.image:
          return 200; // Images start at layer 200
        case MediaType.text:
          return 300; // Text starts at layer 300
      }
    }

    // Find the highest layer for this type and add 1
    final maxLayerForType =
        itemsOfType.map((item) => item.layer).reduce((a, b) => a > b ? a : b);

    return maxLayerForType + 1;
  }

  // Timeline editing methods
  void _resizeMediaItem(
      MediaItem item, double newStartTime, double newEndTime) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id
            ? i.copyWith(startTime: newStartTime, endTime: newEndTime)
            : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  void _moveMediaItem(MediaItem item, double newStartTime) {
    setState(() {
      final duration = item.endTime - item.startTime;
      final newEndTime = newStartTime + duration;

      final items = _currentProject.mediaItems.map((i) {
        return i.id == item.id
            ? i.copyWith(startTime: newStartTime, endTime: newEndTime)
            : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  void _updateMediaItemFromResize(MediaItem updatedItem) {
    setState(() {
      final items = _currentProject.mediaItems.map((i) {
        return i.id == updatedItem.id ? updatedItem : i;
      }).toList();

      _currentProject = _currentProject.copyWith(
        mediaItems: items,
        updatedAt: DateTime.now(),
      );
    });
  }

  Widget _applyTransform(Widget widget, TransformSettings transform) {
    return Transform(
      alignment: Alignment.center,
      transform: Matrix4.identity()
        ..scale(transform.scaleX, transform.scaleY)
        ..rotateZ(
            transform.rotation * 3.14159 / 180) // Convert degrees to radians
        ..translate(transform.translateX, transform.translateY),
      child: transform.flipHorizontal || transform.flipVertical
          ? Transform.flip(
              flipX: transform.flipHorizontal,
              flipY: transform.flipVertical,
              child: widget,
            )
          : widget,
    );
  }

  Widget _applyFilter(Widget widget, FilterSettings filter) {
    // For now, apply basic color filters using ColorFiltered
    // In a real implementation, you'd use more sophisticated image processing

    ColorFilter? colorFilter;

    switch (filter.type) {
      case FilterType.blackWhite:
        colorFilter = const ColorFilter.matrix([
          0.2126,
          0.7152,
          0.0722,
          0,
          0,
          0.2126,
          0.7152,
          0.0722,
          0,
          0,
          0.2126,
          0.7152,
          0.0722,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.sepia:
        colorFilter = const ColorFilter.matrix([
          0.393,
          0.769,
          0.189,
          0,
          0,
          0.349,
          0.686,
          0.168,
          0,
          0,
          0.272,
          0.534,
          0.131,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.vintage:
        colorFilter = const ColorFilter.matrix([
          0.9,
          0.5,
          0.1,
          0,
          0,
          0.3,
          0.8,
          0.1,
          0,
          0,
          0.2,
          0.3,
          0.5,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.warm:
        colorFilter = const ColorFilter.matrix([
          1.2,
          0,
          0,
          0,
          0,
          0,
          1.0,
          0,
          0,
          0,
          0,
          0,
          0.8,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      case FilterType.cool:
        colorFilter = const ColorFilter.matrix([
          0.8,
          0,
          0,
          0,
          0,
          0,
          1.0,
          0,
          0,
          0,
          0,
          0,
          1.2,
          0,
          0,
          0,
          0,
          0,
          1,
          0,
        ]);
        break;
      default:
        // For other filters, apply intensity-based modifications
        final intensity = filter.intensity;
        if (filter.parameters.containsKey('brightness')) {
          final brightness = filter.parameters['brightness']! * intensity;
          colorFilter = ColorFilter.matrix([
            1,
            0,
            0,
            0,
            brightness * 255,
            0,
            1,
            0,
            0,
            brightness * 255,
            0,
            0,
            1,
            0,
            brightness * 255,
            0,
            0,
            0,
            1,
            0,
          ]);
        }
        break;
    }

    if (colorFilter != null) {
      widget = ColorFiltered(
        colorFilter: colorFilter,
        child: widget,
      );
    }

    // Apply blur if specified
    if (filter.type == FilterType.blur &&
        filter.parameters.containsKey('radius')) {
      final radius = filter.parameters['radius']! * filter.intensity;
      // Note: For real blur effect, you'd need to use ImageFilter.blur
      // This is a simplified version
      widget = Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: radius,
              spreadRadius: radius / 2,
            ),
          ],
        ),
        child: widget,
      );
    }

    return widget;
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    const gridSize = 20.0;

    // Draw vertical lines
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Draw horizontal lines
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

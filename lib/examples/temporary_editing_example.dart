import 'package:flutter/material.dart';
import '../models/video_project.dart';
import '../services/temporary_video_service.dart';

/// Example demonstrating how to use the temporary video editing features
class TemporaryEditingExample {
  final TemporaryVideoService _tempService = TemporaryVideoService.instance;

  /// Example 1: Basic Trim Operation
  Future<void> basicTrimExample() async {
    // Simulate a video file
    const videoPath = '/path/to/video.mp4';
    const videoDuration = 120.0; // 2 minutes
    const mediaItemId = 'video_001';

    // Step 1: Create temporary edit state
    final tempState = _tempService.createTemporaryEditState(
      mediaItemId: mediaItemId,
      originalFilePath: videoPath,
      originalDuration: videoDuration,
    );

    print('Created temporary edit state for: $mediaItemId');

    // Step 2: Apply trim (remove first 10 seconds and last 20 seconds)
    final trimmedState = _tempService.applyTemporaryTrim(
      mediaItemId: mediaItemId,
      trimStart: 10.0,  // Start at 10 seconds
      trimEnd: 100.0,   // End at 100 seconds
    );

    print('Applied trim: ${trimmedState.trimStart}s - ${trimmedState.trimEnd}s');
    print('Effective duration: ${trimmedState.getEffectiveDuration()}s');

    // Step 3: Preview the result
    final preview = await _tempService.previewTemporaryEdits(mediaItemId);
    print('Preview result: $preview');
  }

  /// Example 2: Multiple Cut Operations
  Future<void> multipleCutsExample() async {
    const videoPath = '/path/to/video.mp4';
    const videoDuration = 180.0; // 3 minutes
    const mediaItemId = 'video_002';

    // Create temporary edit state
    _tempService.createTemporaryEditState(
      mediaItemId: mediaItemId,
      originalFilePath: videoPath,
      originalDuration: videoDuration,
    );

    // Cut 1: Remove 30-45 seconds (15 seconds removed)
    _tempService.applyTemporaryCut(
      mediaItemId: mediaItemId,
      cutStart: 30.0,
      cutEnd: 45.0,
    );

    // Cut 2: Remove 90-120 seconds (30 seconds removed)
    _tempService.applyTemporaryCut(
      mediaItemId: mediaItemId,
      cutStart: 90.0,
      cutEnd: 120.0,
    );

    // Get timeline segments after cuts
    final segments = _tempService.getTimelineSegments(mediaItemId);
    print('Timeline segments after cuts:');
    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      print('Segment ${i + 1}: ${segment.originalStart}s-${segment.originalEnd}s '
            '-> ${segment.virtualStart}s-${segment.virtualEnd}s');
    }

    // Check effective duration
    final effectiveDuration = _tempService.getEffectiveDuration(mediaItemId);
    print('Original duration: ${videoDuration}s');
    print('Effective duration after cuts: ${effectiveDuration}s');
    print('Total removed: ${videoDuration - effectiveDuration}s');
  }

  /// Example 3: Position Mapping
  void positionMappingExample() {
    const mediaItemId = 'video_003';
    const videoDuration = 100.0;

    // Setup with cuts
    _tempService.createTemporaryEditState(
      mediaItemId: mediaItemId,
      originalFilePath: '/path/to/video.mp4',
      originalDuration: videoDuration,
    );

    // Cut out 20-30 seconds
    _tempService.applyTemporaryCut(
      mediaItemId: mediaItemId,
      cutStart: 20.0,
      cutEnd: 30.0,
    );

    // Test position mapping
    final testPositions = [10.0, 25.0, 40.0, 60.0];
    
    print('Position mapping examples:');
    for (final virtualPos in testPositions) {
      final originalPos = _tempService.virtualToOriginalPosition(
        mediaItemId: mediaItemId,
        virtualPosition: virtualPos,
      );
      
      final isVisible = _tempService.isPositionVisible(
        mediaItemId: mediaItemId,
        virtualPosition: virtualPos,
      );
      
      print('Virtual ${virtualPos}s -> Original ${originalPos}s (Visible: $isVisible)');
    }
  }

  /// Example 4: Complex Editing Workflow
  Future<void> complexWorkflowExample() async {
    const mediaItemId = 'video_004';
    const videoDuration = 300.0; // 5 minutes

    print('=== Complex Editing Workflow Example ===');

    // Step 1: Create temporary state
    _tempService.createTemporaryEditState(
      mediaItemId: mediaItemId,
      originalFilePath: '/path/to/long_video.mp4',
      originalDuration: videoDuration,
    );
    print('1. Created temporary edit state');

    // Step 2: Trim the video (remove intro and outro)
    _tempService.applyTemporaryTrim(
      mediaItemId: mediaItemId,
      trimStart: 15.0,   // Remove first 15 seconds
      trimEnd: 270.0,    // Remove last 30 seconds
    );
    print('2. Applied trim: 15s - 270s');

    // Step 3: Remove multiple unwanted sections
    final cutsToApply = [
      {'start': 45.0, 'end': 60.0},   // Remove 15 seconds
      {'start': 120.0, 'end': 140.0}, // Remove 20 seconds
      {'start': 200.0, 'end': 225.0}, // Remove 25 seconds
    ];

    for (int i = 0; i < cutsToApply.length; i++) {
      final cut = cutsToApply[i];
      _tempService.applyTemporaryCut(
        mediaItemId: mediaItemId,
        cutStart: cut['start']!,
        cutEnd: cut['end']!,
      );
      print('3.${i + 1}. Applied cut: ${cut['start']}s - ${cut['end']}s');
    }

    // Step 4: Preview the final result
    final preview = await _tempService.previewTemporaryEdits(mediaItemId);
    print('4. Preview result:');
    print('   - Original duration: ${preview['originalDuration']}s');
    print('   - Effective duration: ${preview['effectiveDuration']}s');
    print('   - Total removed: ${preview['originalDuration'] - preview['effectiveDuration']}s');
    print('   - Number of segments: ${(preview['segments'] as List).length}');

    // Step 5: Show timeline segments
    final segments = _tempService.getTimelineSegments(mediaItemId);
    print('5. Final timeline segments:');
    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      print('   Segment ${i + 1}: Original(${segment.originalStart}s-${segment.originalEnd}s) '
            '-> Virtual(${segment.virtualStart}s-${segment.virtualEnd}s)');
    }
  }

  /// Example 5: Error Handling
  void errorHandlingExample() {
    print('=== Error Handling Examples ===');

    try {
      // Try to apply trim without creating state first
      _tempService.applyTemporaryTrim(
        mediaItemId: 'nonexistent_video',
        trimStart: 10.0,
        trimEnd: 20.0,
      );
    } catch (e) {
      print('1. Expected error caught: $e');
    }

    // Create state for testing
    const mediaItemId = 'error_test_video';
    _tempService.createTemporaryEditState(
      mediaItemId: mediaItemId,
      originalFilePath: '/path/to/video.mp4',
      originalDuration: 60.0,
    );

    try {
      // Try to apply invalid trim (start > end)
      _tempService.applyTemporaryTrim(
        mediaItemId: mediaItemId,
        trimStart: 50.0,
        trimEnd: 30.0, // Invalid: end before start
      );
    } catch (e) {
      print('2. Invalid trim range error: $e');
    }

    try {
      // Try to cut beyond video duration
      _tempService.applyTemporaryCut(
        mediaItemId: mediaItemId,
        cutStart: 70.0, // Beyond 60s duration
        cutEnd: 80.0,
      );
    } catch (e) {
      print('3. Cut beyond duration error: $e');
    }
  }

  /// Example 6: Performance Testing
  Future<void> performanceTestExample() async {
    print('=== Performance Test Example ===');

    const mediaItemId = 'performance_test_video';
    const videoDuration = 3600.0; // 1 hour video

    final stopwatch = Stopwatch()..start();

    // Create state
    _tempService.createTemporaryEditState(
      mediaItemId: mediaItemId,
      originalFilePath: '/path/to/long_video.mp4',
      originalDuration: videoDuration,
    );

    // Apply many cuts
    for (int i = 0; i < 50; i++) {
      final start = i * 60.0 + 10.0; // Every minute, cut 10 seconds
      final end = start + 10.0;
      
      if (end < videoDuration) {
        _tempService.applyTemporaryCut(
          mediaItemId: mediaItemId,
          cutStart: start,
          cutEnd: end,
        );
      }
    }

    stopwatch.stop();
    print('Applied 50 cuts in ${stopwatch.elapsedMilliseconds}ms');

    // Test position mapping performance
    stopwatch.reset();
    stopwatch.start();

    for (int i = 0; i < 1000; i++) {
      final virtualPos = (i / 1000.0) * videoDuration;
      _tempService.virtualToOriginalPosition(
        mediaItemId: mediaItemId,
        virtualPosition: virtualPos,
      );
    }

    stopwatch.stop();
    print('1000 position mappings in ${stopwatch.elapsedMilliseconds}ms');

    // Test timeline segments generation
    stopwatch.reset();
    stopwatch.start();

    for (int i = 0; i < 100; i++) {
      _tempService.getTimelineSegments(mediaItemId);
    }

    stopwatch.stop();
    print('100 timeline segment generations in ${stopwatch.elapsedMilliseconds}ms');
  }

  /// Run all examples
  Future<void> runAllExamples() async {
    print('🎬 Running Temporary Video Editing Examples\n');

    await basicTrimExample();
    print('\n' + '='*50 + '\n');

    await multipleCutsExample();
    print('\n' + '='*50 + '\n');

    positionMappingExample();
    print('\n' + '='*50 + '\n');

    await complexWorkflowExample();
    print('\n' + '='*50 + '\n');

    errorHandlingExample();
    print('\n' + '='*50 + '\n');

    await performanceTestExample();
    print('\n' + '='*50 + '\n');

    print('✅ All examples completed successfully!');
  }
}

/// Widget to demonstrate the temporary editing features in UI
class TemporaryEditingDemoWidget extends StatefulWidget {
  const TemporaryEditingDemoWidget({super.key});

  @override
  State<TemporaryEditingDemoWidget> createState() => _TemporaryEditingDemoWidgetState();
}

class _TemporaryEditingDemoWidgetState extends State<TemporaryEditingDemoWidget> {
  final TemporaryVideoService _tempService = TemporaryVideoService.instance;
  final String _demoVideoId = 'demo_video';
  String _status = 'Ready';
  List<String> _log = [];

  @override
  void initState() {
    super.initState();
    _initializeDemoVideo();
  }

  void _initializeDemoVideo() {
    _tempService.createTemporaryEditState(
      mediaItemId: _demoVideoId,
      originalFilePath: '/demo/video.mp4',
      originalDuration: 120.0,
    );
    _addLog('Demo video initialized (120s duration)');
  }

  void _addLog(String message) {
    setState(() {
      _log.add('${DateTime.now().toString().substring(11, 19)}: $message');
      if (_log.length > 10) _log.removeAt(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Temporary Editing Demo'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status: $_status', style: const TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Text('Effective Duration: ${_tempService.getEffectiveDuration(_demoVideoId)}s'),
                    Text('Has Active Edits: ${_tempService.hasActiveTemporaryEdits(_demoVideoId)}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Action buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _applyTrim,
                  child: const Text('Apply Trim (10-100s)'),
                ),
                ElevatedButton(
                  onPressed: _applyCut,
                  child: const Text('Apply Cut (30-40s)'),
                ),
                ElevatedButton(
                  onPressed: _previewEdits,
                  child: const Text('Preview Edits'),
                ),
                ElevatedButton(
                  onPressed: _resetEdits,
                  child: const Text('Reset Edits'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Log
            const Text('Activity Log:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: ListView.builder(
                  itemCount: _log.length,
                  itemBuilder: (context, index) {
                    return Text(
                      _log[index],
                      style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _applyTrim() {
    try {
      _tempService.applyTemporaryTrim(
        mediaItemId: _demoVideoId,
        trimStart: 10.0,
        trimEnd: 100.0,
      );
      _addLog('Applied trim: 10s - 100s');
      setState(() {
        _status = 'Trim Applied';
      });
    } catch (e) {
      _addLog('Error applying trim: $e');
    }
  }

  void _applyCut() {
    try {
      _tempService.applyTemporaryCut(
        mediaItemId: _demoVideoId,
        cutStart: 30.0,
        cutEnd: 40.0,
      );
      _addLog('Applied cut: 30s - 40s');
      setState(() {
        _status = 'Cut Applied';
      });
    } catch (e) {
      _addLog('Error applying cut: $e');
    }
  }

  void _previewEdits() async {
    try {
      final preview = await _tempService.previewTemporaryEdits(_demoVideoId);
      _addLog('Preview: ${preview['effectiveDuration']}s effective duration');
      setState(() {
        _status = 'Preview Generated';
      });
    } catch (e) {
      _addLog('Error generating preview: $e');
    }
  }

  void _resetEdits() {
    try {
      _tempService.resetTemporaryEdits(_demoVideoId);
      _addLog('All edits reset');
      setState(() {
        _status = 'Reset Complete';
      });
    } catch (e) {
      _addLog('Error resetting edits: $e');
    }
  }
}

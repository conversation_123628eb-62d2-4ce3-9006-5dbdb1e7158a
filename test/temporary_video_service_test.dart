import 'package:flutter_test/flutter_test.dart';
import '../lib/services/temporary_video_service.dart';
import '../lib/models/video_project.dart';

void main() {
  group('TemporaryVideoService Tests', () {
    late TemporaryVideoService service;
    const testMediaItemId = 'test_video_001';
    const testVideoPath = '/test/video.mp4';
    const testDuration = 120.0; // 2 minutes

    setUp(() {
      service = TemporaryVideoService.instance;
      service.clearAllTemporaryStates(); // Clean state before each test
    });

    tearDown(() {
      service.clearAllTemporaryStates(); // Clean state after each test
    });

    group('Basic Operations', () {
      test('should create temporary edit state', () {
        final state = service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        expect(state.originalFilePath, equals(testVideoPath));
        expect(state.originalDuration, equals(testDuration));
        expect(state.isActive, isFalse);
        expect(state.cutPoints, isEmpty);
        expect(state.trimStart, isNull);
        expect(state.trimEnd, isNull);
      });

      test('should apply temporary trim', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        final trimmedState = service.applyTemporaryTrim(
          mediaItemId: testMediaItemId,
          trimStart: 10.0,
          trimEnd: 100.0,
        );

        expect(trimmedState.trimStart, equals(10.0));
        expect(trimmedState.trimEnd, equals(100.0));
        expect(trimmedState.isActive, isTrue);
        expect(trimmedState.getEffectiveDuration(), equals(90.0));
      });

      test('should apply temporary cut', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        final cutState = service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 45.0,
        );

        expect(cutState.cutPoints.length, equals(1));
        expect(cutState.cutPoints.first.startTime, equals(30.0));
        expect(cutState.cutPoints.first.endTime, equals(45.0));
        expect(cutState.isActive, isTrue);
        expect(cutState.getEffectiveDuration(), equals(105.0)); // 120 - 15
      });

      test('should reset temporary edits', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        // Apply some edits
        service.applyTemporaryTrim(
          mediaItemId: testMediaItemId,
          trimStart: 10.0,
          trimEnd: 100.0,
        );

        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 45.0,
        );

        // Reset
        service.resetTemporaryEdits(testMediaItemId);

        final state = service.getTemporaryEditState(testMediaItemId);
        expect(state?.isActive, isFalse);
        expect(state?.trimStart, isNull);
        expect(state?.trimEnd, isNull);
        expect(state?.cutPoints, isEmpty);
      });
    });

    group('Position Mapping', () {
      test('should map virtual to original position without edits', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        final originalPos = service.virtualToOriginalPosition(
          mediaItemId: testMediaItemId,
          virtualPosition: 50.0,
        );

        expect(originalPos, equals(50.0));
      });

      test('should map virtual to original position with trim', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        service.applyTemporaryTrim(
          mediaItemId: testMediaItemId,
          trimStart: 20.0,
          trimEnd: 100.0,
        );

        // Virtual position 10 should map to original position 30 (20 + 10)
        final originalPos = service.virtualToOriginalPosition(
          mediaItemId: testMediaItemId,
          virtualPosition: 10.0,
        );

        expect(originalPos, equals(30.0));
      });

      test('should map virtual to original position with cuts', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        // Cut out 30-40 seconds (10 seconds removed)
        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 40.0,
        );

        // Virtual position 35 should map to original position 45 (after the cut)
        final originalPos = service.virtualToOriginalPosition(
          mediaItemId: testMediaItemId,
          virtualPosition: 35.0,
        );

        expect(originalPos, equals(45.0));
      });

      test('should check position visibility', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        // Cut out 30-40 seconds
        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 40.0,
        );

        // Position 25 should be visible (before cut)
        expect(service.isPositionVisible(
          mediaItemId: testMediaItemId,
          virtualPosition: 25.0,
        ), isTrue);

        // Position 35 should be visible (after cut, mapped to original 45)
        expect(service.isPositionVisible(
          mediaItemId: testMediaItemId,
          virtualPosition: 35.0,
        ), isTrue);
      });
    });

    group('Timeline Segments', () {
      test('should generate single segment without cuts', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        final segments = service.getTimelineSegments(testMediaItemId);

        expect(segments.length, equals(1));
        expect(segments.first.originalStart, equals(0.0));
        expect(segments.first.originalEnd, equals(testDuration));
        expect(segments.first.virtualStart, equals(0.0));
        expect(segments.first.virtualEnd, equals(testDuration));
      });

      test('should generate single segment with trim only', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        service.applyTemporaryTrim(
          mediaItemId: testMediaItemId,
          trimStart: 20.0,
          trimEnd: 100.0,
        );

        final segments = service.getTimelineSegments(testMediaItemId);

        expect(segments.length, equals(1));
        expect(segments.first.originalStart, equals(20.0));
        expect(segments.first.originalEnd, equals(100.0));
        expect(segments.first.virtualStart, equals(0.0));
        expect(segments.first.virtualEnd, equals(80.0));
      });

      test('should generate multiple segments with cuts', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        // Cut out 30-40 and 70-80
        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 40.0,
        );

        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 70.0,
          cutEnd: 80.0,
        );

        final segments = service.getTimelineSegments(testMediaItemId);

        expect(segments.length, equals(3));
        
        // First segment: 0-30
        expect(segments[0].originalStart, equals(0.0));
        expect(segments[0].originalEnd, equals(30.0));
        expect(segments[0].virtualStart, equals(0.0));
        expect(segments[0].virtualEnd, equals(30.0));

        // Second segment: 40-70 (mapped to virtual 30-60)
        expect(segments[1].originalStart, equals(40.0));
        expect(segments[1].originalEnd, equals(70.0));
        expect(segments[1].virtualStart, equals(30.0));
        expect(segments[1].virtualEnd, equals(60.0));

        // Third segment: 80-120 (mapped to virtual 60-100)
        expect(segments[2].originalStart, equals(80.0));
        expect(segments[2].originalEnd, equals(120.0));
        expect(segments[2].virtualStart, equals(60.0));
        expect(segments[2].virtualEnd, equals(100.0));
      });
    });

    group('Complex Scenarios', () {
      test('should handle trim and multiple cuts together', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        // Trim to 10-110 (100 seconds)
        service.applyTemporaryTrim(
          mediaItemId: testMediaItemId,
          trimStart: 10.0,
          trimEnd: 110.0,
        );

        // Cut out 30-40 and 70-80 (within trimmed range)
        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 40.0,
        );

        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 70.0,
          cutEnd: 80.0,
        );

        final effectiveDuration = service.getEffectiveDuration(testMediaItemId);
        // Original: 120s, Trim: 100s, Cuts: -20s = 80s
        expect(effectiveDuration, equals(80.0));

        final segments = service.getTimelineSegments(testMediaItemId);
        expect(segments.length, equals(3));
      });

      test('should handle overlapping cuts gracefully', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        // Apply overlapping cuts
        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 50.0,
        );

        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 40.0,
          cutEnd: 60.0,
        );

        // Should handle overlapping cuts
        final state = service.getTemporaryEditState(testMediaItemId);
        expect(state?.cutPoints.length, equals(2));
        
        // Effective duration calculation should account for overlaps
        final effectiveDuration = service.getEffectiveDuration(testMediaItemId);
        expect(effectiveDuration, lessThan(testDuration));
      });
    });

    group('Error Handling', () {
      test('should throw error when applying trim without state', () {
        expect(
          () => service.applyTemporaryTrim(
            mediaItemId: 'nonexistent',
            trimStart: 10.0,
            trimEnd: 20.0,
          ),
          throwsException,
        );
      });

      test('should throw error when applying cut without state', () {
        expect(
          () => service.applyTemporaryCut(
            mediaItemId: 'nonexistent',
            cutStart: 10.0,
            cutEnd: 20.0,
          ),
          throwsException,
        );
      });

      test('should handle invalid media item ID gracefully', () {
        final result = service.getTemporaryEditState('nonexistent');
        expect(result, isNull);

        final hasEdits = service.hasActiveTemporaryEdits('nonexistent');
        expect(hasEdits, isFalse);

        final duration = service.getEffectiveDuration('nonexistent');
        expect(duration, equals(0.0));
      });
    });

    group('Performance Tests', () {
      test('should handle many cuts efficiently', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: 3600.0, // 1 hour
        );

        final stopwatch = Stopwatch()..start();

        // Apply 100 cuts
        for (int i = 0; i < 100; i++) {
          final start = i * 30.0 + 5.0;
          final end = start + 5.0;
          
          if (end < 3600.0) {
            service.applyTemporaryCut(
              mediaItemId: testMediaItemId,
              cutStart: start,
              cutEnd: end,
            );
          }
        }

        stopwatch.stop();
        
        // Should complete within reasonable time (adjust threshold as needed)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));

        final state = service.getTemporaryEditState(testMediaItemId);
        expect(state?.cutPoints.length, greaterThan(90));
      });

      test('should handle position mapping efficiently', () {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: 3600.0,
        );

        // Apply some cuts
        for (int i = 0; i < 10; i++) {
          service.applyTemporaryCut(
            mediaItemId: testMediaItemId,
            cutStart: i * 300.0 + 10.0,
            cutEnd: i * 300.0 + 20.0,
          );
        }

        final stopwatch = Stopwatch()..start();

        // Test 1000 position mappings
        for (int i = 0; i < 1000; i++) {
          service.virtualToOriginalPosition(
            mediaItemId: testMediaItemId,
            virtualPosition: i * 3.0,
          );
        }

        stopwatch.stop();
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });

    group('Preview Functionality', () {
      test('should generate preview for edits', () async {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        service.applyTemporaryTrim(
          mediaItemId: testMediaItemId,
          trimStart: 10.0,
          trimEnd: 100.0,
        );

        service.applyTemporaryCut(
          mediaItemId: testMediaItemId,
          cutStart: 30.0,
          cutEnd: 40.0,
        );

        final preview = await service.previewTemporaryEdits(testMediaItemId);

        expect(preview['hasEdits'], isTrue);
        expect(preview['originalDuration'], equals(testDuration));
        expect(preview['effectiveDuration'], equals(80.0)); // 90 - 10
        expect(preview['trimStart'], equals(10.0));
        expect(preview['trimEnd'], equals(100.0));
        expect(preview['cutPoints'], hasLength(1));
      });

      test('should generate preview for no edits', () async {
        service.createTemporaryEditState(
          mediaItemId: testMediaItemId,
          originalFilePath: testVideoPath,
          originalDuration: testDuration,
        );

        final preview = await service.previewTemporaryEdits(testMediaItemId);

        expect(preview['hasEdits'], isFalse);
        expect(preview['originalDuration'], equals(testDuration));
        expect(preview['effectiveDuration'], equals(testDuration));
      });
    });
  });
}

# Video Editor App - VE Mides

Aplikasi video editor se<PERSON><PERSON> seperti CapCut yang dibuat dengan Flutter. Aplikasi ini menyediakan fitur-fitur dasar untuk editing video seperti menambahkan teks, gambar, audio, multi-layer, filter, dan transform.

## Fitur Utama

### ✅ Fitur yang Sudah Diimplementasikan:
- **🆕 Empty Project** - <PERSON><PERSON> dengan canvas kosong tanpa perlu import video dulu
- **Import Video** - Import video dari galeri atau rekam video baru
- **Video Preview** - Preview video dengan kontrol play/pause
- **Timeline** - Timeline visual untuk mengelola media items
- **Add Text Overlay** - Menambahkan teks dengan customizable:
  - Font size (12-72px)
  - Warna teks (8 pilihan warna)
  - Posisi X dan Y
  - Waktu mulai dan selesai
  - Preview real-time
- **Add Image Overlay** - Menambahkan gambar overlay pada video
- **Add Audio** - Menambahkan file audio ke video
- **Add Video Layers** - Menambahkan video tambahan sebagai overlay
- **Multi-Layer Support** - Sistem layer untuk mengatur urutan media
- **Filter Effects** - 14 jenis filter:
  - None, Vintage, Black & White, Sepia
  - Blur, Sharpen, Brightness, Contrast
  - Saturation, Hue, Vignette
  - Warm, Cool, Dramatic
- **Transform Tools** - Transformasi media items:
  - Scale (X dan Y terpisah atau terkunci)
  - Rotation (dengan quick buttons)
  - Position (translate X dan Y)
  - Flip (horizontal dan vertical)
  - Crop (semua sisi)
- **Layer Management** - Kelola urutan dan visibilitas layer
- **Duplicate Media** - Duplikasi media items
- **Multi-Layer Timeline** - Timeline dengan sistem layer:
  - **Layer Labels**: Label L0, L1, L2, dst. dengan tipe media
  - **Layer Separation**: Setiap layer terpisah dengan jelas
  - **Auto Layer Assignment**: Video (L0), Audio (L1), Image (L2), Text (L3+)
  - **Visual Layer Indicators**: Setiap item menampilkan nomor layer
  - **Scrollable Timeline**: Timeline dapat di-scroll untuk layer banyak
- **Timeline Editing** - Edit langsung di timeline:
  - Drag & drop untuk memindah media items
  - Resize handles untuk mengubah durasi
  - Visual feedback saat editing
  - Layer-aware positioning
- **Crop & Resize** - Tool untuk crop dan resize media items:
  - Crop dari semua sisi (left, top, right, bottom)
  - Resize width dan height
  - Reposition X dan Y
  - Real-time preview
- **Interactive Resize** - Resize langsung di preview video:
  - **Bounding Box**: Kotak seleksi dengan 8 handle resize
  - **Drag & Drop**: Pindahkan item dengan drag
  - **Visual Selection**: Border biru dan shadow untuk item terpilih
  - **Keyboard Shortcuts**: Delete key untuk hapus item terpilih
  - **Tap to Select**: Tap item untuk select/deselect
- **Trim Mode** - Mode untuk memotong durasi video
- **Cut Mode** - Mode untuk memotong bagian tertentu dari video
- **Export Video** - Export hasil editing ke file video
- **Multiple Video Support** - Support multiple video dalam satu project:
  - **Video Layers**: Video utama (layer 0) + overlay video (layer 1+)
  - **Main Video Player**: Video layer 0 menggunakan VideoPlayer penuh
  - **Overlay Placeholders**: Video overlay ditampilkan sebagai placeholder visual
  - **Timeline Integration**: Semua video muncul di timeline dengan layer masing-masing
  - **MediaCodec Optimized**: Menghindari konflik MediaCodec dengan single player
- **Video Merger** - Menggabungkan multiple video:
  - **Multi-Selection**: Pilih 2 atau lebih video dari galeri
  - **Drag to Reorder**: Atur urutan video dengan drag & drop
  - **Preview List**: Lihat daftar video yang akan digabung
  - **Merge Process**: Proses penggabungan dengan loading indicator
  - **Auto Navigation**: Otomatis buka editor dengan video hasil merge

## 🎬 Sistem Multi-Layer Timeline (BARU!)

### **Grouped Timeline - Organized by Media Type:**
- **Video Track**: Semua video layers (0-99) dalam satu track
- **Audio Track**: Semua audio layers (100-199) dalam satu track
- **Image Track**: Semua image layers (200-299) dalam satu track
- **Text Track**: Semua text layers (300+) dalam satu track

### **Fitur Timeline Baru:**
- **Type-based Grouping**: Media dikelompokkan berdasarkan tipe untuk editing yang lebih mudah
- **Track Labels**: Label dengan icon untuk setiap tipe media (Video, Audio, Image, Text)
- **Layer Indicators**: Setiap item menampilkan nomor layer dalam lingkaran
- **Zoom Controls**: Zoom in/out timeline dengan slider dan tombol (1%-100%)
- **Horizontal Scroll**: Timeline dapat di-scroll horizontal untuk durasi panjang
- **Dynamic Width**: Lebar timeline menyesuaikan durasi dan zoom level
- **Click to Seek**: Klik di timeline untuk jump ke posisi tertentu
- **Drag & Drop**: Drag item untuk mengubah posisi waktu
- **Resize Handles**: Handle kiri/kanan untuk mengubah durasi item
- **Visual Feedback**: Warna berbeda untuk setiap tipe media
- **Auto-Assignment**: Media baru otomatis ditempatkan di layer yang sesuai
- **Compact Design**: Timeline lebih compact dengan 4 track utama

### **Cara Menggunakan Timeline:**
1. **Zoom Controls**: Gunakan slider atau tombol +/- untuk zoom timeline (1%-100%)
2. **Horizontal Scroll**: Drag timeline ke kiri/kanan untuk navigasi
3. **Click to Seek**: Klik di timeline untuk jump ke posisi tertentu
4. **Menambah Media**: Media baru otomatis ditempatkan di layer yang tepat
5. **Edit Posisi**: Drag item untuk mengubah posisi waktu
6. **Edit Durasi**: Drag handle kiri/kanan untuk resize
7. **Layer Management**: Gunakan tombol "Layers" untuk mengatur urutan layer
8. **Visual Feedback**: Lihat nomor layer di setiap item untuk orientasi

## 🎯 Interactive Resize & Selection

### **Fitur Bounding Box:**
- **8 Resize Handles**: Corner dan edge handles untuk resize presisi
- **Visual Selection**: Border biru dengan shadow untuk item terpilih
- **Drag to Move**: Drag item untuk mengubah posisi
- **Tap to Select**: Tap item untuk select, tap area kosong untuk deselect

### **Resize Handles:**
- **Corner Handles**: Resize width dan height bersamaan
- **Edge Handles**: Resize satu dimensi (width atau height saja)
- **Constraint**: Minimum size 50x50 pixel
- **Boundary**: Item tidak bisa keluar dari area preview

### **Keyboard Shortcuts:**
- **Delete Key**: Hapus item yang sedang dipilih
- **Tap Empty Area**: Deselect semua item

### **Cara Menggunakan:**
1. **Select Item**: Tap pada text/image di preview video
2. **Resize**: Drag handle biru untuk mengubah ukuran
3. **Move**: Drag area tengah item untuk memindah posisi
4. **Delete**: Tekan Delete key atau gunakan tombol delete di timeline
5. **Deselect**: Tap area kosong di preview

## 🎥 Multiple Video Support

### **Fitur Multiple Video (Updated):**
- **Main Video Player**: Video layer 0 menggunakan VideoPlayer penuh untuk performa optimal
- **Overlay Placeholders**: Video layer 1+ ditampilkan sebagai placeholder visual
- **Layer-based System**: Video di layer 0-99, audio 100-199, image 200-299, text 300+
- **Timeline Integration**: Semua video muncul di grouped timeline
- **MediaCodec Safe**: Menghindari konflik MediaCodec dengan single player approach

### **Cara Menggunakan Multiple Video:**
1. **Add First Video**: Import video pertama atau mulai dengan empty project
2. **Add More Videos**: Tap "Video" → pilih video tambahan
3. **Auto Concatenation**: Video baru otomatis ditempatkan setelah video sebelumnya
4. **Timeline Navigation**: Klik timeline untuk switch antar video
5. **Full-screen Display**: Video ditampilkan full-screen tanpa bounding box
6. **Timeline Control**: Semua video muncul di timeline dengan track Video

### **Technical Features:**
- **Video Concatenation**: Video baru otomatis ditempatkan setelah video sebelumnya
- **Dynamic Duration**: Durasi project otomatis bertambah sesuai video yang ditambahkan
- **Smart Video Switching**: Video player otomatis switch ke video yang aktif di timeline
- **Timeline-based Playback**: Preview video berubah sesuai posisi timeline
- **Relative Positioning**: Video di-seek ke posisi relatif dalam video tersebut
- **Full-screen Video**: Video ditampilkan full-screen tanpa bounding box
- **Memory Efficient**: Optimized untuk performa dan stabilitas
- **Clean Interface**: Video tidak memiliki resize handles untuk pengalaman yang lebih clean

## 🎬 Video Merger

### **Fitur Merge Videos:**
- **Multi-Selection**: Pilih 2 atau lebih video dari galeri
- **Reorderable List**: Drag & drop untuk mengatur urutan video
- **Visual Preview**: Lihat daftar video dengan thumbnail dan info
- **Progress Tracking**: Loading indicator selama proses merge
- **Error Handling**: Pesan error yang informatif

### **Cara Menggunakan Video Merger:**
1. **Buka Home Screen**: Tap tombol "Merge Videos" di quick actions
2. **Add Videos**: Tap "Add Video" untuk memilih video dari galeri
3. **Reorder**: Drag video untuk mengatur urutan penggabungan
4. **Remove**: Tap icon delete untuk menghapus video dari list
5. **Merge**: Tap "Merge Videos" untuk memulai proses penggabungan
6. **Wait**: Tunggu proses selesai (loading dialog akan muncul)
7. **Edit**: Otomatis masuk ke editor dengan video hasil merge

### **Persyaratan Merge:**
- Minimum 2 video untuk bisa merge
- Video akan digabung sesuai urutan di list
- Hasil merge otomatis dibuka di editor
- Support semua format video yang didukung aplikasi

## 🔧 Technical Solutions

### **MediaCodec Error Fix:**
- **Problem**: Multiple VideoPlayerController menyebabkan MediaCodec conflict
- **Solution**: Single player approach dengan visual placeholders
- **Implementation**:
  - Main video (layer 0) menggunakan VideoPlayer penuh
  - Overlay videos (layer 1+) menggunakan placeholder visual
  - Timeline tetap menampilkan semua video layers
  - Transform dan positioning tetap berfungsi untuk placeholders

### **Timeline Optimization:**
- **Grouped by Type**: Media dikelompokkan berdasarkan tipe (Video, Audio, Image, Text)
- **Zoom System**: Timeline dapat di-zoom dari 1% hingga 100% untuk presisi editing
- **Horizontal Scroll**: Timeline dapat di-scroll horizontal untuk project panjang
- **Dynamic Width**: Lebar timeline menyesuaikan durasi dan zoom level
- **Click to Seek**: Klik di timeline untuk jump ke posisi tertentu
- **Smart Video Switching**: Preview otomatis switch ke video yang aktif
- **Compact Design**: 4 track utama menggantikan unlimited layers
- **Better UX**: Lebih mudah untuk organize dan edit media

### 🚧 Fitur yang Perlu Pengembangan Lebih Lanjut:
- **Real Video Overlay**: Implementasi multiple video player yang stabil
- **Video Processing** - FFmpeg integration untuk fitur lengkap
- **Project Management** - Save/load project ke local storage
- **Advanced Editing** - Transitions, effects, filters yang lebih kompleks
- **Real-time Preview** - Preview filter dan transform secara real-time
- **Multiple Video Tracks** - Support untuk multiple video layers dengan VideoPlayerController terpisah

## Struktur Aplikasi

```
lib/
├── main.dart                     # Entry point aplikasi
├── models/
│   └── video_project.dart        # Model data untuk project, media items, filter, transform
├── screens/
│   ├── home_screen.dart          # Halaman utama
│   └── editor_screen.dart        # Halaman editor video
├── widgets/
│   ├── video_timeline.dart       # Widget timeline video
│   ├── text_overlay_widget.dart  # Widget untuk menambah text overlay
│   ├── filter_widget.dart        # Widget untuk memilih dan mengatur filter
│   ├── transform_widget.dart     # Widget untuk transformasi (scale, rotate, flip, crop)
│   └── multi_layer_widget.dart   # Widget untuk mengelola layer
├── services/
│   └── video_service.dart        # Service untuk video processing
└── utils/
    └── constants.dart            # Konstanta aplikasi
```

## Dependencies

```yaml
dependencies:
  flutter:
    sdk: flutter
  video_player: ^2.9.5           # Video player
  image_picker: ^1.1.2           # Image/video picker
  file_picker: ^10.1.9           # File picker untuk audio
  path_provider: ^2.1.5          # Path provider
  permission_handler: ^12.0.0+1  # Permission handler
```

## Instalasi dan Setup

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd ve_mides
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Setup permissions**

   **Android** - Tambahkan ke `android/app/src/main/AndroidManifest.xml`:
   ```xml
   <uses-permission android:name="android.permission.CAMERA" />
   <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
   <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
   <uses-permission android:name="android.permission.RECORD_AUDIO" />
   ```

   **iOS** - Tambahkan ke `ios/Runner/Info.plist`:
   ```xml
   <key>NSCameraUsageDescription</key>
   <string>This app needs camera access to record videos</string>
   <key>NSPhotoLibraryUsageDescription</key>
   <string>This app needs photo library access to import videos</string>
   <key>NSMicrophoneUsageDescription</key>
   <string>This app needs microphone access to record audio</string>
   ```

4. **Run aplikasi**
   ```bash
   flutter run
   ```

## Cara Penggunaan

### 1. Halaman Utama
- **Start New Project**: Tap untuk memulai project baru
  - Pilih "Import from Gallery" atau "Record New Video"
- **Merge Videos**: Tap untuk menggabungkan multiple video
  - Pilih 2+ video → atur urutan → merge
- **Recent Projects**: Lihat project yang pernah dibuat (akan diimplementasikan)

### 2. Editor Video
- **Play/Pause**: Kontrol pemutaran video
- **Seek**: Navigasi maju/mundur 10 detik
- **Multi-Layer Timeline**: Timeline dengan sistem layer yang terorganisir
  - Layer labels di sebelah kiri (L0, L1, L2, dst.)
  - Media items dengan nomor layer indicator
  - Drag & drop untuk reposition
  - Resize handles untuk mengubah durasi
- **Tools**: Akses tools untuk editing

### 3. Menambah Media
- **Text**: Tap "Text" → atur font, warna, posisi, waktu → otomatis ke layer 300+
- **Image**: Tap "Image" → pilih dari galeri → otomatis ke layer 200+
- **Audio**: Tap "Audio" → pilih file audio → otomatis ke layer 100+
- **Video**: Tap "Video" → pilih video overlay → otomatis ke layer 1, 2, 3, dst.
  - Video utama di layer 0, video tambahan di layer berikutnya
  - Setiap video dapat diposisi dan resize independen
  - Semua video tersinkronisasi dengan timeline

### 4. Filter dan Transform
- **Filter**: Tap "Filter" → pilih dari 14 jenis filter → atur intensity dan parameter
- **Transform**: Tap "Transform" → atur scale, rotation, position, flip, crop
- **Layers**: Tap "Layers" → kelola urutan dan visibilitas layer
- **Crop**: Tap "Crop" → crop dan resize media item yang dipilih
- **Duplicate**: Tap "Duplicate" → duplikasi media item yang dipilih

### 4.5. Timeline Editing
- **Drag Item**: Tap dan drag media item untuk mengubah posisi waktu
- **Resize Duration**: Drag handle kiri/kanan untuk mengubah durasi
- **Layer Positioning**: Item otomatis ditempatkan di layer yang sesuai
- **Visual Feedback**: Nomor layer ditampilkan di setiap item
- **Context Menu**: Long press item untuk menu edit/delete

### 4.6. Interactive Resize di Preview
- **Select Item**: Tap text/image di preview untuk select
- **Bounding Box**: Kotak biru dengan 8 handle resize muncul
- **Resize**: Drag handle untuk mengubah ukuran
- **Move**: Drag area tengah untuk memindah posisi
- **Deselect**: Tap area kosong atau tekan Delete key

### 5. Trim dan Cut
- **Trim Mode**: Aktifkan untuk memotong durasi video
- **Cut Mode**: Aktifkan untuk memotong bagian tertentu

### 6. Merge Videos (Fitur Baru!)
- **Akses**: Tap "Merge Videos" di home screen
- **Add Videos**: Tap "Add Video" untuk pilih dari galeri
- **Reorder**: Drag video untuk atur urutan
- **Remove**: Tap icon delete untuk hapus video
- **Merge**: Tap "Merge Videos" untuk gabungkan
- **Auto Edit**: Hasil merge otomatis buka di editor

### 7. Export
- Tap icon download di app bar
- Video akan diexport ke folder Documents/VideoEditor
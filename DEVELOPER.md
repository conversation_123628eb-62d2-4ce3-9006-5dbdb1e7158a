# Developer Documentation

## Arsitektur Aplikasi

### Model Layer

#### VideoProject
Model utama yang merepresentasikan project video editing:
```dart
class VideoProject {
  final String id;
  final String name;
  final String? mainVideoPath;
  final List<MediaItem> mediaItems;
  final double duration;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

#### MediaItem
Model untuk setiap item media (video, audio, image, text):
```dart
class MediaItem {
  final String id;
  final MediaType type;
  final String? filePath;
  final String? text;
  final double startTime;
  final double endTime;
  final double? x, y, width, height;
  final double? fontSize;
  final int? textColor;
  final double opacity;
  final double volume;
  final FilterSettings? filterSettings;
  final TransformSettings? transformSettings;
  final int layer;
}
```

#### FilterSettings
Model untuk pengaturan filter:
```dart
class FilterSettings {
  final FilterType type;
  final double intensity;
  final Map<String, double> parameters;
}
```

#### TransformSettings
Model untuk pengaturan transformasi:
```dart
class TransformSettings {
  final double scaleX, scaleY;
  final double rotation;
  final double translateX, translateY;
  final bool flipHorizontal, flipVertical;
  final double cropLeft, cropTop, cropRight, cropBottom;
}
```

### Service Layer

#### VideoService
Singleton service untuk video processing:
```dart
class VideoService {
  static VideoService get instance => _instance ??= VideoService._();
  
  Future<double> getVideoDuration(String videoPath);
  Future<String?> trimVideo({required String inputPath, required double startTime, required double endTime});
  Future<String?> cutVideo({required String inputPath, required double cutStart, required double cutEnd});
  Future<String?> addAudioToVideo({required String videoPath, required String audioPath});
  Future<String?> addTextOverlay({required String videoPath, required String text, ...});
  Future<String?> addImageOverlay({required String videoPath, required String imagePath, ...});
  Future<String?> exportProject(VideoProject project);
}
```

### Widget Layer

#### Custom Widgets

**VideoTimeline**
- Custom painter untuk timeline
- Multi-track support
- Drag & drop functionality
- Time ruler dan grid

**FilterWidget**
- Grid layout untuk filter selection
- Parameter controls dengan sliders
- Real-time preview
- Intensity adjustment

**TransformWidget**
- Tab-based interface
- Scale, rotation, position, flip, crop controls
- Quick action buttons
- Reset functionality

**MultiLayerWidget**
- Reorderable list
- Layer management controls
- Visibility toggles
- Drag handle untuk reordering

**TextOverlayWidget**
- Text input dengan formatting
- Color picker
- Position controls
- Time range selection

## State Management

### EditorScreen State
```dart
class _EditorScreenState extends State<EditorScreen> {
  late VideoPlayerController _videoController;
  late VideoProject _currentProject;
  
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isExporting = false;
  double _currentPosition = 0;
  double _videoDuration = 0;
  
  // Trim/Cut state
  double? _trimStart;
  double? _trimEnd;
  bool _isTrimMode = false;
  bool _isCutMode = false;
}
```

### State Updates
- `setState()` untuk UI updates
- Project state management
- Media item updates
- Timeline synchronization

## Video Processing

### Current Implementation
Saat ini menggunakan implementasi sederhana tanpa FFmpeg:
```dart
Future<String?> trimVideo({
  required String inputPath,
  required double startTime,
  required double endTime,
}) async {
  // Simplified implementation - just copy file
  final outputPath = '$tempDir/trimmed_${DateTime.now().millisecondsSinceEpoch}.mp4';
  await File(inputPath).copy(outputPath);
  return outputPath;
}
```

### FFmpeg Integration (Future)
Untuk implementasi lengkap, gunakan FFmpeg:
```dart
Future<String?> trimVideo({
  required String inputPath,
  required double startTime,
  required double endTime,
}) async {
  final command = '-i "$inputPath" -ss $startTime -t ${endTime - startTime} -c copy "$outputPath"';
  final session = await FFmpegKit.execute(command);
  final returnCode = await session.getReturnCode();
  return ReturnCode.isSuccess(returnCode) ? outputPath : null;
}
```

## Filter Implementation

### Color Matrix Filters
```dart
Widget _applyFilter(Widget widget, FilterSettings filter) {
  ColorFilter? colorFilter;
  
  switch (filter.type) {
    case FilterType.blackWhite:
      colorFilter = const ColorFilter.matrix([
        0.2126, 0.7152, 0.0722, 0, 0,
        0.2126, 0.7152, 0.0722, 0, 0,
        0.2126, 0.7152, 0.0722, 0, 0,
        0, 0, 0, 1, 0,
      ]);
      break;
    // ... other filters
  }
  
  return ColorFiltered(colorFilter: colorFilter, child: widget);
}
```

### Transform Implementation
```dart
Widget _applyTransform(Widget widget, TransformSettings transform) {
  return Transform(
    alignment: Alignment.center,
    transform: Matrix4.identity()
      ..scale(transform.scaleX, transform.scaleY)
      ..rotateZ(transform.rotation * 3.14159 / 180)
      ..translate(transform.translateX, transform.translateY),
    child: transform.flipHorizontal || transform.flipVertical
        ? Transform.flip(
            flipX: transform.flipHorizontal,
            flipY: transform.flipVertical,
            child: widget,
          )
        : widget,
  );
}
```

## Layer Management

### Layer Sorting
```dart
List<Widget> _buildOverlayItems() {
  final sortedItems = List<MediaItem>.from(_currentProject.mediaItems)
    ..sort((a, b) => a.layer.compareTo(b.layer));
  
  // Render items in layer order
  for (final item in sortedItems) {
    // Apply transforms and filters
    // Position on screen
  }
}
```

### Layer Operations
```dart
int _getNextAvailableLayer() {
  if (_currentProject.mediaItems.isEmpty) return 0;
  
  final maxLayer = _currentProject.mediaItems
      .map((item) => item.layer)
      .reduce((a, b) => a > b ? a : b);
  
  return maxLayer + 1;
}
```

## Performance Considerations

### Memory Management
- Dispose VideoPlayerController properly
- Clear temporary files
- Optimize image loading
- Limit concurrent operations

### UI Performance
- Use CustomPainter untuk timeline
- Efficient widget rebuilding
- Lazy loading untuk media items
- Debounce slider updates

### File Management
```dart
Future<String> get _tempDir async {
  final directory = await getTemporaryDirectory();
  return directory.path;
}

Future<String> get _outputDir async {
  final directory = await getApplicationDocumentsDirectory();
  final outputDir = Directory('${directory.path}/VideoEditor');
  if (!await outputDir.exists()) {
    await outputDir.create(recursive: true);
  }
  return outputDir.path;
}
```

## Testing

### Unit Tests
```dart
void main() {
  group('VideoService', () {
    test('should get video duration', () async {
      final service = VideoService.instance;
      final duration = await service.getVideoDuration('test_video.mp4');
      expect(duration, greaterThan(0));
    });
  });
}
```

### Widget Tests
```dart
void main() {
  testWidgets('FilterWidget should display filters', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: FilterWidget(
        onFilterChanged: (filter) {},
        onCancel: () {},
      ),
    ));
    
    expect(find.text('Apply Filter'), findsOneWidget);
    expect(find.byType(GridView), findsOneWidget);
  });
}
```

## Debugging

### Common Issues
1. **Video not playing**: Check file path dan permissions
2. **Filter not applying**: Verify ColorFilter matrix
3. **Transform not working**: Check Matrix4 operations
4. **Layer ordering**: Verify layer numbers dan sorting

### Debug Tools
```dart
void _debugPrintProject() {
  print('Project: ${_currentProject.name}');
  print('Duration: ${_currentProject.duration}');
  print('Media Items: ${_currentProject.mediaItems.length}');
  for (final item in _currentProject.mediaItems) {
    print('  - ${item.type}: ${item.startTime}-${item.endTime} (Layer ${item.layer})');
  }
}
```

## Deployment

### Build Commands
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release

# Windows
flutter build windows --release

# macOS
flutter build macos --release
```

### Platform-specific Configurations
- Android: Permissions di AndroidManifest.xml
- iOS: Permissions di Info.plist
- Windows: File associations
- macOS: Sandbox permissions

## Contributing

### Code Style
- Follow Dart style guide
- Use meaningful variable names
- Add comments untuk complex logic
- Keep methods under 50 lines

### Pull Request Process
1. Fork repository
2. Create feature branch
3. Write tests
4. Update documentation
5. Submit pull request

### Issue Reporting
- Use issue templates
- Provide reproduction steps
- Include device information
- Attach logs if applicable
